<template>
  <div class="visual-function-composer">
    <!-- 头部工具栏 -->
    <div class="composer-header">
      <t-space>
        <t-button theme="default" @click="testComposition" :disabled="steps.length === 0">
          <template #icon>
            <play-icon />
          </template>
          测试预览
        </t-button>
        <t-button theme="default" @click="clearAll" :disabled="steps.length === 0">
          <template #icon>
            <clear-icon />
          </template>
          清空
        </t-button>
        <t-divider layout="vertical" />
        <span class="step-count">共 {{ steps.length }} 个步骤</span>
      </t-space>
    </div>

    <!-- 主要编排区域 -->
    <div class="composer-body">
      <!-- 左侧：变量列表 -->
      <div class="variable-panel">
        <div class="panel-header">
          <h4>变量列表</h4>
          <t-space size="small">
            <t-button size="small" theme="primary" @click="showAddVariableDialog = true">
              <template #icon>
                <add-icon />
              </template>
              添加变量
            </t-button>
            <t-button
              size="small"
              theme="default"
              @click="showTestValueDialog = true"
              :disabled="variables.length === 0"
            >
              <template #icon>
                <setting-icon />
              </template>
              设置测试值
            </t-button>
          </t-space>
        </div>
        <div class="panel-content">
          <div class="variable-tree-container">
            <variable-tree-manager
              :variables="variables"
              @node-click="onVariableTreeClick"
              @node-dblclick="onVariableTreeDblClick"
              @edit-variable="onVariableEdit"
              @delete-variable="onVariableDelete"
            />
          </div>
        </div>
      </div>

      <!-- 中间：函数编排区域 -->
      <div class="composition-panel">
        <div class="panel-header">
          <h4>函数编排</h4>
          <t-button size="small" theme="primary" @click="addFunctionStep" :loading="showFunctionDialog">
            <template #icon>
              <add-icon />
            </template>
            添加函数
          </t-button>
        </div>
        <div class="panel-content">
          <div v-if="steps.length === 0" class="empty-state">
            <t-icon name="function" size="48px" style="color: var(--td-text-color-placeholder)" />
            <p>点击"添加函数"开始编排</p>
          </div>
          <div v-else class="function-steps" ref="stepsContainerRef">
            <div
              v-for="(step, index) in steps"
              :key="step.id"
              class="function-step"
              :class="{ active: activeStepIndex === index }"
              draggable="true"
              @dragstart="onStepDragStart($event, index)"
              @dragover="onStepDragOver($event, index)"
              @drop="onStepDrop($event, index)"
              @dragend="onStepDragEnd"
            >
              <div class="step-header" @click="setActiveStep(index)">
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-info">
                  <div class="step-name">{{ step.displayName }}</div>
                  <div class="step-function">{{ step.functionName }}</div>
                </div>
                <div class="step-actions">
                  <t-button size="small" variant="text" @click.stop="editStep(index)">
                    <template #icon>
                      <edit-icon />
                    </template>
                  </t-button>
                  <t-button size="small" variant="text" theme="danger" @click.stop="removeStep(index)">
                    <template #icon>
                      <delete-icon />
                    </template>
                  </t-button>
                </div>
              </div>

              <!-- 参数配置区域 -->
              <div v-if="activeStepIndex === index" class="step-config">
                <div class="config-section">
                  <h5>参数配置</h5>
                  <div
                    v-for="(param, paramIndex) in step.parameters"
                    :key="paramIndex"
                    class="param-item"
                    :class="{
                      'param-error': isParameterInvalid(index, paramIndex),
                      'param-required': param.required && !param.value,
                    }"
                  >
                    <label>{{ param.name }}{{ param.required ? ' *' : '' }}</label>
                    <div class="param-input">
                      <t-select
                        v-model="param.type"
                        :options="paramTypeOptions"
                        @change="onParamTypeChange(index, paramIndex)"
                      />
                      <t-input
                        v-if="param.type === 'text'"
                        v-model="param.value"
                        :placeholder="`请输入${param.name}`"
                      />
                      <t-popup
                        v-else-if="param.type === 'variable'"
                        placement="bottom-left"
                        trigger="click"
                        :visible="variablePopupVisible[`${index}-${paramIndex}`]"
                        @visible-change="(visible) => handleVariablePopupChange(visible, index, paramIndex)"
                      >
                        <t-input
                          v-model="param.value"
                          placeholder="选择变量"
                          readonly
                          :suffix-icon="() => h(ChevronDownIcon)"
                        />
                        <template #content>
                          <div class="variable-tree-popup">
                            <variable-tree
                              :variable-list="getAllVariablesForSelection(index)"
                              @dblclick="(data) => onSelectVariableForParam(data, index, paramIndex)"
                            />
                          </div>
                        </template>
                      </t-popup>
                      <t-select
                        v-else-if="param.type === 'previousResult'"
                        v-model="param.value"
                        :options="getPreviousResultOptions(index)"
                        placeholder="选择上一步结果"
                      />
                      <t-input v-else v-model="param.value" :placeholder="`请输入${param.name}`" />
                    </div>
                  </div>
                </div>

                <div class="config-section">
                  <h5>输出变量</h5>
                  <t-input v-model="step.outputVariable" placeholder="输出变量名（可选）" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：输出预览 -->
      <div class="output-panel">
        <div class="panel-header">
          <h4>输出预览</h4>
        </div>
        <div class="panel-content">
          <div v-if="outputData" class="output-results">
            <div v-if="outputData.success" class="success-result">
              <div class="result-header">
                <t-icon name="check-circle" style="color: var(--td-success-color)" />
                <span>执行成功</span>
                <span class="execution-time">{{ outputData.executionTime }}ms</span>
              </div>

              <!-- 步骤结果 -->
              <div class="step-results">
                <h5>步骤执行结果</h5>
                <div v-if="outputData.stepResults && outputData.stepResults.length > 0">
                  <div
                    v-for="(stepResult, index) in outputData.stepResults"
                    :key="stepResult.stepId"
                    class="step-result-item"
                  >
                    <div class="step-result-header">
                      <span class="step-number">{{ index + 1 }}</span>
                      <span class="step-name">{{ stepResult.stepName }}</span>
                      <span class="step-time">{{ stepResult.executionTimeMs }}ms</span>
                    </div>
                    <div class="step-result-content">
                      <CodePreview
                        :code="JSON.stringify(stepResult.result, null, 2)"
                        language="json"
                        :show-copy="true"
                        :max-height="200"
                        :is-dark="false"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <!-- 最终结果 -->
              <div class="final-result">
                <h5>最终结果</h5>
                <div class="result-content">
                  <CodePreview
                    :code="JSON.stringify(outputData.result, null, 2)"
                    language="json"
                    :show-copy="true"
                    :max-height="300"
                    :is-dark="false"
                  />
                </div>
              </div>
            </div>

            <div v-else class="error-result">
              <div class="result-header">
                <t-icon name="close-circle" style="color: var(--td-error-color)" />
                <span>执行失败</span>
                <span class="execution-time">{{ outputData.executionTime }}ms</span>
              </div>
              <div class="error-content">
                <p>{{ outputData.error }}</p>
              </div>
            </div>
          </div>
          <div v-else class="empty-state">
            <t-icon name="view-module" size="48px" style="color: var(--td-text-color-placeholder)" />
            <p>执行测试后显示结果</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 函数选择对话框 -->
    <t-dialog
      v-model:visible="showFunctionDialog"
      header="选择函数"
      width="1000px"
      height="700px"
      :footer="false"
      :destroy-on-close="true"
      placement="center"
      :draggable="true"
      :show-overlay="true"
    >
      <enhanced-function-list @dblclick="onSelectFunction" />
    </t-dialog>

    <!-- 添加变量对话框 -->
    <t-dialog
      v-model:visible="showAddVariableDialog"
      :header="editingVariableId ? '编辑变量' : '添加变量'"
      width="700px"
      @confirm="handleAddVariable"
    >
      <t-form ref="variableFormRef" :data="newVariable" layout="vertical">
        <t-form-item label="变量名称" name="name" :rules="[{ required: true, message: '请输入变量名称' }]">
          <t-input v-model="newVariable.name" placeholder="例如：user.name" />
        </t-form-item>
        <t-form-item label="变量类型" name="type">
          <t-select v-model="newVariable.type" :options="variableTypeOptions" />
        </t-form-item>
        <t-form-item label="变量来源" name="source">
          <t-select v-model="newVariable.source" :options="variableSourceOptions" @change="onVariableSourceChange" />
        </t-form-item>

        <!-- 临时变量、局部变量、全局变量直接选择 -->
        <t-form-item
          v-if="['temp', 'local', 'global'].includes(newVariable.source)"
          label="选择变量"
          name="selectedVariable"
        >
          <div class="variable-selector">
            <t-tabs v-model="variableSelectorTab" size="medium">
              <t-tab-panel v-if="newVariable.source === 'temp'" value="temp" label="临时变量">
                <variable-tree
                  v-model:active-id="newVariable.selectedVariable"
                  :variable-list="actionFlowStore.currentVariables"
                  @dblclick="onSelectExistingVariable"
                />
              </t-tab-panel>
              <t-tab-panel v-if="newVariable.source === 'local'" value="local" label="局部变量">
                <variable-tree
                  v-model:active-id="newVariable.selectedVariable"
                  :variable-list="actionFlowStore.localVariables"
                  @dblclick="onSelectExistingVariable"
                />
              </t-tab-panel>
              <t-tab-panel v-if="newVariable.source === 'global'" value="global" label="全局变量">
                <variable-tree
                  v-model:active-id="newVariable.selectedVariable"
                  :variable-list="actionFlowStore.globalVariables"
                  @dblclick="onSelectExistingVariable"
                />
              </t-tab-panel>
            </t-tabs>
          </div>
        </t-form-item>

        <!-- 其他类型使用editor -->
        <t-form-item v-else label="变量值" name="value">
          <div class="value-input-container">
            <t-radio-group v-model="newVariable.valueInputType" size="small" style="margin-bottom: 8px">
              <t-radio-button value="simple">简单输入</t-radio-button>
              <t-radio-button value="editor">代码编辑器</t-radio-button>
            </t-radio-group>
            <t-input
              v-if="newVariable.valueInputType === 'simple'"
              v-model="newVariable.value"
              placeholder="请输入变量值"
            />
            <editor
              v-else
              v-model:value="newVariable.value"
              language="javascript"
              style="height: 150px"
              :read-only="false"
            />
          </div>
        </t-form-item>

        <t-form-item label="描述" name="description">
          <t-textarea v-model="newVariable.description" placeholder="变量描述" />
        </t-form-item>
      </t-form>
    </t-dialog>

    <!-- 设置测试值对话框 -->
    <t-dialog
      v-model:visible="showTestValueDialog"
      header="设置测试值"
      width="800px"
      height="600px"
      @confirm="handleSetTestValues"
    >
      <div class="test-values-container">
        <div class="test-values-header">
          <t-space>
            <t-button size="small" theme="primary" @click="generateTestValuesFromVariables">
              <template #icon>
                <refresh-icon />
              </template>
              从变量生成
            </t-button>
            <t-button size="small" theme="default" @click="clearTestValues">
              <template #icon>
                <clear-icon />
              </template>
              清空
            </t-button>
          </t-space>
        </div>
        <div class="test-values-content">
          <editor v-model:value="testValuesJson" language="json" style="height: 400px" :read-only="false" />
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script lang="tsx">
export default {
  name: 'VisualFunctionComposer',
};
</script>

<script setup lang="tsx">
import { ref, watch, onMounted, onActivated, nextTick, h } from 'vue';
import {
  AddIcon,
  PlayIcon,
  ClearIcon,
  EditIcon,
  DeleteIcon,
  ChevronDownIcon,
  SettingIcon,
  RefreshIcon,
} from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import EnhancedFunctionList from './EnhancedFunctionList.vue';
import VariableTree from './VariableTree.vue';
import VariableTreeManager from './VariableTreeManager.vue';
import Editor from '@/components/editor/index.vue';
import CodePreview from '@/components/code-preview/index.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import { api, Services } from '@/api/system';

// 类型定义
interface VisualFunctionStep {
  id: string;
  functionName: string;
  functionType: 'csharp' | 'javascript' | 'builtin';
  displayName: string;
  description?: string;
  parameters: VisualFunctionParameter[];
  outputVariable?: string;
  order: number;
}

interface VisualFunctionParameter {
  name: string;
  type: 'text' | 'variable' | 'previousResult';
  value: any;
  required: boolean;
}

// Props
const props = defineProps<{
  modelValue?: VisualFunctionStep[];
  inputData?: any;
}>();

// Emits
const emits = defineEmits<{
  'update:modelValue': [value: VisualFunctionStep[]];
  test: [steps: VisualFunctionStep[]];
  change: [steps: VisualFunctionStep[]];
}>();

// 响应式数据
const steps = ref<VisualFunctionStep[]>(props.modelValue || []);

const activeStepIndex = ref<number>(-1);
const showFunctionDialog = ref(false);
const showAddVariableDialog = ref(false);
const showTestValueDialog = ref(false);
const variableSelectorTab = ref('temp');
const editingVariableId = ref<string>('');
const testValues = ref<Record<string, any>>({});
const testValuesJson = ref<string>('{}');

const outputData = ref<any>(null);

// 拖拽相关状态
const draggedStepIndex = ref<number>(-1);
const stepsContainerRef = ref<HTMLElement>();

// 变量选择弹窗状态
const variablePopupVisible = ref<Record<string, boolean>>({});

// Store
const actionFlowStore = useActionFlowStore();

// 变量管理 - 默认为空
const variables = ref<VariableItem[]>([]);

interface VariableItem {
  id: string;
  name: string;
  type: string;
  source: 'input' | 'temp' | 'local' | 'global' | 'custom';
  value?: any;
  description?: string;
  selectedVariable?: string;
  valueInputType?: 'simple' | 'editor';
}

// 新变量表单数据
const newVariable = ref<Omit<VariableItem, 'id'>>({
  name: '',
  type: 'string',
  source: 'custom',
  value: '',
  description: '',
  selectedVariable: '',
  valueInputType: 'simple',
});

// 参数类型选项
const paramTypeOptions = [
  { label: '固定值', value: 'text' },
  { label: '变量', value: 'variable' },
  { label: '上一步结果', value: 'previousResult' },
];

// 变量类型选项
const variableTypeOptions = [
  { label: '字符串', value: 'string' },
  { label: '数字', value: 'number' },
  { label: '布尔值', value: 'boolean' },
  { label: '对象', value: 'object' },
  { label: '数组', value: 'array' },
];

// 变量来源选项
const variableSourceOptions = [
  { label: '输入数据', value: 'input' },
  { label: '临时变量', value: 'temp' },
  { label: '局部变量', value: 'local' },
  { label: '全局变量', value: 'global' },
  { label: '自定义变量', value: 'custom' },
];

// 生命周期钩子
onMounted(async () => {
  // 使用 store 中的防抖加载方法
  await actionFlowStore.debouncedLoadGlobalVariables();
});

onActivated(async () => {
  // 使用 store 中的防抖加载方法
  await actionFlowStore.debouncedLoadGlobalVariables();

  // 确保在组件激活时重新同步 props
  const normalizedValue = props.modelValue || [];
  if (JSON.stringify(normalizedValue) !== JSON.stringify(steps.value)) {
    isInternalUpdate.value = true;
    steps.value = normalizedValue;
    nextTick(() => {
      isInternalUpdate.value = false;
    });
  }
});

// 计算属性
// const inputData = computed(() => props.inputData); // 暂时未使用

// 监听器
const isInternalUpdate = ref(false);

watch(
  () => steps.value,
  (newSteps) => {
    if (!isInternalUpdate.value) {
      emits('update:modelValue', newSteps);
      emits('change', newSteps);
    }
  },
  { deep: true },
);

watch(
  () => props.modelValue,
  (newValue) => {
    // 修复：即使 newValue 为空数组或 undefined 也要更新
    const normalizedNewValue = newValue || [];
    if (JSON.stringify(normalizedNewValue) !== JSON.stringify(steps.value)) {
      isInternalUpdate.value = true;
      steps.value = normalizedNewValue;
      nextTick(() => {
        isInternalUpdate.value = false;
      });
    }
  },
);

// 方法
const addFunctionStep = () => {
  showFunctionDialog.value = true;
};

// 变量管理方法
const getVariableSourceLabel = (source: string) => {
  const labels = {
    input: '输入',
    temp: '临时',
    local: '局部',
    global: '全局',
    custom: '自定义',
  };
  return labels[source as keyof typeof labels] || source;
};

// 变量编辑和删除方法
const onVariableEdit = (variable: VariableItem) => {
  // 设置编辑状态
  newVariable.value = {
    name: variable.name,
    type: variable.type,
    source: variable.source,
    value: variable.value,
    description: variable.description,
    selectedVariable: variable.selectedVariable || '',
    valueInputType: variable.valueInputType || 'simple',
  };
  editingVariableId.value = variable.id;
  showAddVariableDialog.value = true;
};

const onVariableDelete = (variable: VariableItem) => {
  const index = variables.value.findIndex((v) => v.id === variable.id);
  if (index > -1) {
    variables.value.splice(index, 1);
    // 删除变量后验证并清理无效的参数引用
    nextTick(() => {
      validateAndCleanParameters();
    });
  }
};

const addVariable = (variable: Omit<VariableItem, 'id'>) => {
  const newVariableItem: VariableItem = {
    ...variable,
    id: generateId(),
  };
  variables.value.push(newVariableItem);
};

// 变量来源改变处理
const onVariableSourceChange = (source: string) => {
  // 重置相关字段
  newVariable.value.selectedVariable = '';
  newVariable.value.value = '';

  // 设置对应的tab
  if (['temp', 'local', 'global'].includes(source)) {
    variableSelectorTab.value = source;
  }
};

// 选择已有变量 - 只添加选中的节点和其子节点，不添加父节点
const onSelectExistingVariable = (data: any) => {
  if (data.path) {
    // 跳过ROOT节点本身
    if (data.path === 'ROOT') {
      return;
    }

    // 获取实际的变量数据 - data.item 是 TreeData 结构，实际数据在 data.item.data 中
    const variableData = data.item?.data;

    // 获取节点的实际名称（不包含父路径）
    const nodeName = data.item?.label || data.path.split('.').pop();

    // 为了避免重名冲突，如果是深层节点，使用简化的路径
    let finalVariableName = nodeName;
    const pathParts = data.path.split('.');
    if (pathParts.length > 1) {
      // 对于深层节点，使用最后两级作为变量名，避免冲突
      finalVariableName = pathParts.slice(-2).join('_');
    }

    // 设置变量名
    newVariable.value.name = finalVariableName;
    newVariable.value.selectedVariable = data.path; // 保持原始路径用于引用

    if (variableData?.type) {
      newVariable.value.type = variableData.type;
    }

    // 只添加选中的节点和其子节点
    if (variableData) {
      addSelectedVariableWithChildren(variableData, finalVariableName);
    }

    // 选择变量后自动保存并关闭对话框
    handleAddVariable();
  }
};

// 递归添加变量及其子节点（用于批量导入时）
const addVariableWithChildren = (variableData: any, basePath: string) => {
  // 跳过ROOT节点本身
  if (basePath === 'ROOT') {
    // 如果是ROOT节点，直接处理其子节点
    if (variableData.children && variableData.children.length > 0) {
      variableData.children.forEach((child: any) => {
        // 对于ROOT节点的直接子节点，路径不包含ROOT前缀
        const childPath = child.key || child.path;
        addVariableWithChildren(child, childPath);
      });
    }
    return;
  }

  // 总是添加当前变量（无论是容器节点还是叶子节点）
  const currentVariable: Omit<VariableItem, 'id'> = {
    name: basePath,
    type: variableData.type || 'object',
    source: variableData.value?.variableType === 'global' ? 'global' : newVariable.value.source,
    value: variableData.value?.variableValue || variableData.value,
    description: variableData.description || basePath,
    selectedVariable: basePath,
    valueInputType: 'simple',
  };

  // 检查是否已存在，如果存在则更新，不存在则添加
  const existingIndex = variables.value.findIndex((v) => v.name === basePath);
  if (existingIndex === -1) {
    addVariable(currentVariable);
  } else {
    // 更新现有变量
    variables.value[existingIndex] = {
      ...variables.value[existingIndex],
      ...currentVariable,
      id: variables.value[existingIndex].id, // 保持原有ID
    };
  }

  // 递归添加子节点
  if (variableData.children && variableData.children.length > 0) {
    variableData.children.forEach((child: any) => {
      const childPath = `${basePath}.${child.key}`;
      addVariableWithChildren(child, childPath);
    });
  }
};

// 只添加选中的节点和其子节点（用于单个变量选择时）
const addSelectedVariableWithChildren = (variableData: any, selectedPath: string) => {
  // 只添加选中的变量本身
  const currentVariable: Omit<VariableItem, 'id'> = {
    name: selectedPath,
    type: variableData.type || 'object',
    source: variableData.value?.variableType === 'global' ? 'global' : newVariable.value.source,
    value: variableData.value?.variableValue || variableData.value,
    description: variableData.description || selectedPath,
    selectedVariable: selectedPath,
    valueInputType: 'simple',
  };

  // 检查是否已存在，如果存在则更新，不存在则添加
  const existingIndex = variables.value.findIndex((v) => v.name === selectedPath);
  if (existingIndex === -1) {
    addVariable(currentVariable);
  } else {
    // 更新现有变量
    variables.value[existingIndex] = {
      ...variables.value[existingIndex],
      ...currentVariable,
      id: variables.value[existingIndex].id, // 保持原有ID
    };
  }

  // 递归添加子节点
  if (variableData.children && variableData.children.length > 0) {
    variableData.children.forEach((child: any) => {
      const childPath = `${selectedPath}.${child.key}`;
      addSelectedVariableWithChildren(child, childPath);
    });
  }
};

const handleAddVariable = () => {
  if (newVariable.value.name) {
    if (editingVariableId.value) {
      // 编辑模式：更新现有变量
      const index = variables.value.findIndex((v) => v.id === editingVariableId.value);
      if (index > -1) {
        variables.value[index] = {
          ...variables.value[index],
          ...newVariable.value,
        };
      }
      editingVariableId.value = '';
    } else {
      // 添加模式：创建新变量
      addVariable(newVariable.value);
    }

    // 重置表单
    newVariable.value = {
      name: '',
      type: 'string',
      source: 'custom',
      value: '',
      description: '',
      selectedVariable: '',
      valueInputType: 'simple',
    };
    showAddVariableDialog.value = false;
  }
};

const onSelectFunction = (func: any) => {
  if (!func) {
    console.error('函数数据为空:', func);
    MessagePlugin.error('请先选择一个函数');
    return;
  }

  if (!func.value) {
    console.error('函数缺少value属性:', func);
    MessagePlugin.error('所选函数数据不完整，缺少value属性');
    return;
  }

  if (!func.script) {
    console.error('函数缺少script属性:', func);
    MessagePlugin.error('所选函数数据不完整，缺少script属性');
    return;
  }

  // 函数的输出变量默认为空
  const newStep: VisualFunctionStep = {
    id: generateId(),
    functionName: func.value,
    functionType: 'builtin',
    displayName: func.label || func.value,
    description: func.remark || '',
    parameters: generateParametersFromScript(func.script || ''),
    outputVariable: '', // 默认为空
    order: steps.value.length,
  };

  steps.value.push(newStep);
  showFunctionDialog.value = false;

  // 立即设置新添加的步骤为活动状态
  activeStepIndex.value = steps.value.length - 1;

  // 确保界面更新后滚动到新添加的步骤
  setTimeout(() => {
    const stepElement = document.querySelector('.function-step:last-child');
    if (stepElement) {
      stepElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, 100);
};

const generateParametersFromScript = (script: string): VisualFunctionParameter[] => {
  // 简单的参数解析逻辑，可以根据需要扩展
  const params: VisualFunctionParameter[] = [];
  const matches = script.match(/\(([^)]*)\)/);

  if (matches && matches[1]) {
    const paramStr = matches[1];
    const paramNames = paramStr
      .split(',')
      .map((p) => p.trim())
      .filter((p) => p && !p.includes('"'));

    paramNames.forEach((name, index) => {
      params.push({
        name: name || `参数${index + 1}`,
        type: 'text',
        value: '',
        required: true,
      });
    });
  }

  return params;
};

const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

const setActiveStep = (index: number) => {
  activeStepIndex.value = activeStepIndex.value === index ? -1 : index;
};

const editStep = (index: number) => {
  setActiveStep(index);
};

const removeStep = (index: number) => {
  steps.value.splice(index, 1);
  if (activeStepIndex.value >= index) {
    activeStepIndex.value = -1;
  }
  // 删除步骤后验证并清理无效的参数引用
  nextTick(() => {
    validateAndCleanParameters();
  });
};

const onParamTypeChange = (stepIndex: number, paramIndex: number) => {
  const param = steps.value[stepIndex].parameters[paramIndex];
  const oldValue = param.value;
  const newType = param.type;

  // 根据新类型智能处理参数值
  if (newType === 'text') {
    // 如果切换到固定值，保留原值（如果是字符串）
    param.value = typeof oldValue === 'string' ? oldValue : '';
  } else if (newType === 'variable') {
    // 如果切换到变量，检查原值是否在变量列表中
    const variableOptions = getVariableOptions();
    const existingVariable = variableOptions.find((opt) => opt.value === oldValue);
    param.value = existingVariable ? oldValue : '';
  } else if (newType === 'previousResult') {
    // 如果切换到上一步结果，检查原值是否在上一步结果列表中
    const previousOptions = getPreviousResultOptions(stepIndex);
    const existingResult = previousOptions.find((opt) => opt.value === oldValue);
    param.value = existingResult ? oldValue : '';
  } else {
    // 其他类型重置为空
    param.value = '';
  }
};

// 获取变量选项
const getVariableOptions = () => {
  return variables.value.map((variable) => ({
    label: `${variable.name} (${getVariableSourceLabel(variable.source)})`,
    value: variable.name,
  }));
};

// 获取上一步结果选项
const getPreviousResultOptions = (currentStepIndex: number) => {
  const options: Array<{ label: string; value: string }> = [];

  // 验证步骤索引的有效性
  if (currentStepIndex < 0 || currentStepIndex >= steps.value.length) {
    console.warn('无效的步骤索引:', currentStepIndex);
    return options;
  }

  for (let i = 0; i < currentStepIndex; i++) {
    const step = steps.value[i];
    if (!step) {
      console.warn('步骤不存在:', i);
      continue;
    }

    if (step.outputVariable && step.outputVariable.trim()) {
      options.push({
        label: `${step.outputVariable} (步骤${i + 1}: ${step.displayName})`,
        value: step.outputVariable,
      });
    } else {
      options.push({
        label: `步骤${i + 1}结果 (${step.displayName})`,
        value: `step_${step.id}_result`, // 使用步骤ID而不是索引，更稳定
      });
    }
  }

  return options;
};

// 验证并清理无效的参数值
const validateAndCleanParameters = () => {
  steps.value.forEach((step, stepIndex) => {
    step.parameters.forEach((param) => {
      if (param.type === 'variable') {
        // 检查变量是否仍然存在
        const variableOptions = getVariableOptions();
        const isValidVariable = variableOptions.some((opt) => opt.value === param.value);
        if (!isValidVariable && param.value) {
          console.warn(`步骤${stepIndex + 1}中的变量参数"${param.name}"引用了不存在的变量"${param.value}"，已清空`);
          param.value = '';
        }
      } else if (param.type === 'previousResult') {
        // 检查上一步结果是否仍然有效
        const previousOptions = getPreviousResultOptions(stepIndex);
        const isValidResult = previousOptions.some((opt) => opt.value === param.value);
        if (!isValidResult && param.value) {
          console.warn(`步骤${stepIndex + 1}中的结果参数"${param.name}"引用了无效的上一步结果"${param.value}"，已清空`);
          param.value = '';
        }
      }
    });
  });
};

// 检查参数配置的完整性
const checkParameterCompleteness = () => {
  const issues: string[] = [];

  steps.value.forEach((step, stepIndex) => {
    step.parameters.forEach((param) => {
      if (param.required && (!param.value || param.value.toString().trim() === '')) {
        issues.push(`步骤${stepIndex + 1}(${step.displayName})的必填参数"${param.name}"未配置`);
      }

      if (param.type === 'variable' && param.value) {
        const variableOptions = getVariableOptions();
        const isValidVariable = variableOptions.some((opt) => opt.value === param.value);
        if (!isValidVariable) {
          issues.push(
            `步骤${stepIndex + 1}(${step.displayName})的参数"${param.name}"引用了不存在的变量"${param.value}"`,
          );
        }
      }

      if (param.type === 'previousResult' && param.value) {
        const previousOptions = getPreviousResultOptions(stepIndex);
        const isValidResult = previousOptions.some((opt) => opt.value === param.value);
        if (!isValidResult) {
          issues.push(
            `步骤${stepIndex + 1}(${step.displayName})的参数"${param.name}"引用了无效的上一步结果"${param.value}"`,
          );
        }
      }
    });
  });

  return issues;
};

// 检查单个参数是否无效
const isParameterInvalid = (stepIndex: number, paramIndex: number) => {
  const step = steps.value[stepIndex];
  if (!step || !step.parameters[paramIndex]) return false;

  const param = step.parameters[paramIndex];

  // 必填参数未填写
  if (param.required && (!param.value || param.value.toString().trim() === '')) {
    return true;
  }

  // 变量类型但引用的变量不存在
  if (param.type === 'variable' && param.value) {
    const variableOptions = getVariableOptions();
    return !variableOptions.some((opt) => opt.value === param.value);
  }

  // 上一步结果类型但引用的结果无效
  if (param.type === 'previousResult' && param.value) {
    const previousOptions = getPreviousResultOptions(stepIndex);
    return !previousOptions.some((opt) => opt.value === param.value);
  }

  return false;
};

const testComposition = async () => {
  if (steps.value.length === 0) return;

  // 执行前验证并清理无效的参数值
  validateAndCleanParameters();

  // 检查参数配置的完整性
  const issues = checkParameterCompleteness();
  if (issues.length > 0) {
    MessagePlugin.warning({
      content: `参数配置存在问题：\n${issues.join('\n')}`,
      duration: 5000,
    });
    return;
  }

  try {
    // 构建输入数据
    const inputData = buildInputDataFromVariables();

    // 调用后端API执行测试
    const response = await executeVisualFunction(steps.value, inputData);

    if (response.success) {
      outputData.value = {
        success: true,
        result: response.result,
        stepResults: response.stepResults || [],
        executionTime: response.executionTimeMs,
        // 测试模式不显示生成的表达式
      };
    } else {
      outputData.value = {
        success: false,
        error: response.errorMessage,
        executionTime: response.executionTimeMs || 0,
      };
    }
  } catch (error) {
    console.error('测试执行失败:', error);
    outputData.value = {
      success: false,
      error: error.message || '测试执行失败',
      executionTime: 0,
    };
  }
};

// 从变量列表构建输入数据 - 优先使用测试值
const buildInputDataFromVariables = () => {
  // 如果有测试值，优先使用测试值
  if (Object.keys(testValues.value).length > 0) {
    // 如果测试值有ROOT节点，提取ROOT节点下的数据
    return testValues.value.ROOT || testValues.value;
  }

  // 否则从变量列表构建
  const inputData: Record<string, any> = {};

  variables.value.forEach((variable) => {
    // 解析变量路径，支持嵌套对象
    const keys = variable.name.split('.');
    let current = inputData;

    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }

    // 设置最终值
    const finalKey = keys[keys.length - 1];
    current[finalKey] = variable.value || getDefaultValueByType(variable.type);
  });

  return inputData;
};

// 根据类型获取默认值
const getDefaultValueByType = (type: string) => {
  switch (type) {
    case 'string':
      return '';
    case 'number':
      return 0;
    case 'boolean':
      return false;
    case 'array':
      return [];
    case 'object':
      return {};
    default:
      return null;
  }
};

// 转换前端数据结构为后端期望的格式
const convertStepsForBackend = (steps: VisualFunctionStep[]) => {
  return steps.map((step) => ({
    id: step.id,
    functionName: step.functionName,
    functionType: step.functionType === 'builtin' ? 0 : step.functionType === 'csharp' ? 1 : 2, // 转换为数字枚举
    displayName: step.displayName,
    description: step.description,
    parameters: step.parameters.map((param) => ({
      name: param.name,
      type: param.type === 'text' ? 0 : param.type === 'variable' ? 1 : 2, // 转换为数字枚举
      value: param.value,
      required: param.required,
    })),
    outputVariable: step.outputVariable,
    order: step.order,
  }));
};

// 调用后端API执行可视化函数
const executeVisualFunction = async (steps: VisualFunctionStep[], inputData: Record<string, any>) => {
  try {
    // 转换数据格式
    const convertedSteps = convertStepsForBackend(steps);

    // 使用GCP标准API调用方式，测试模式不使用编译表达式
    const response = await api.run(Services.visualFunctionExecute, {
      Steps: convertedSteps,
      InputData: inputData,
      GenerateExpressionOnly: false,
      UseCompiledExpression: false, // 测试模式使用解释执行，获取详细步骤信息
    });

    return {
      success: response.success !== false,
      result: response.result,
      stepResults: response.stepResults,
      executionTimeMs: response.executionTimeMs,
      generatedExpression: response.generatedExpression,
      errorMessage: response.errorMessage,
    };
  } catch (error) {
    console.error('调用后端API失败:', error);
    console.error('错误详情:', {
      message: error.message,
      stack: error.stack,
      response: error.response,
    });

    // 返回模拟数据作为后备
    return {
      success: false,
      result: null,
      errorMessage: error.message || '执行失败',
      stepResults: steps.map((step, index) => ({
        stepId: step.id,
        stepName: step.displayName,
        result: `步骤${index + 1}模拟结果`,
        executionTimeMs: Math.random() * 100,
      })),
      executionTimeMs: Math.random() * 500,
      generatedExpression: `// 模拟生成的表达式\nvar result = Utils.${steps[0]?.functionName || 'UNKNOWN'}(...);`,
    };
  }
};

const clearAll = () => {
  steps.value = [];
  activeStepIndex.value = -1;
  outputData.value = null;
};

// 拖拽排序相关方法
const onStepDragStart = (event: DragEvent, index: number) => {
  draggedStepIndex.value = index;
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/plain', index.toString());
  }

  // 添加拖拽样式
  const target = event.target as HTMLElement;
  const stepElement = target.closest('.function-step');
  if (stepElement) {
    stepElement.classList.add('dragging');
  }
};

const onStepDragOver = (event: DragEvent, _index: number) => {
  event.preventDefault();
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move';
  }

  // 添加拖拽悬停样式
  const target = event.currentTarget as HTMLElement;
  target.classList.add('drag-over');
};

const onStepDrop = (event: DragEvent, targetIndex: number) => {
  event.preventDefault();

  const sourceIndex = draggedStepIndex.value;
  if (sourceIndex === -1 || sourceIndex === targetIndex) {
    return;
  }

  // 执行步骤重排序
  const newSteps = [...steps.value];
  const [draggedStep] = newSteps.splice(sourceIndex, 1);
  newSteps.splice(targetIndex, 0, draggedStep);

  // 更新步骤顺序
  newSteps.forEach((step, index) => {
    step.order = index;
  });

  steps.value = newSteps;

  // 更新活动步骤索引
  if (activeStepIndex.value === sourceIndex) {
    activeStepIndex.value = targetIndex;
  } else if (activeStepIndex.value > sourceIndex && activeStepIndex.value <= targetIndex) {
    activeStepIndex.value--;
  } else if (activeStepIndex.value < sourceIndex && activeStepIndex.value >= targetIndex) {
    activeStepIndex.value++;
  }

  // 清除拖拽样式
  const target = event.currentTarget as HTMLElement;
  target.classList.remove('drag-over');

  MessagePlugin.success('步骤顺序已更新');
};

const onStepDragEnd = (event: DragEvent) => {
  draggedStepIndex.value = -1;

  // 清除所有拖拽样式
  const target = event.target as HTMLElement;
  const stepElement = target.closest('.function-step');
  if (stepElement) {
    stepElement.classList.remove('dragging');
  }

  // 清除所有悬停样式
  const allSteps = document.querySelectorAll('.function-step');
  allSteps.forEach((step) => {
    step.classList.remove('drag-over');
  });
};

// 变量选择相关方法
const handleVariablePopupChange = (visible: boolean, stepIndex: number, paramIndex: number) => {
  const key = `${stepIndex}-${paramIndex}`;
  variablePopupVisible.value[key] = visible;
};

const onSelectVariableForParam = (data: any, stepIndex: number, paramIndex: number) => {
  if (data.path) {
    steps.value[stepIndex].parameters[paramIndex].value = data.path;
    // 关闭弹窗
    const key = `${stepIndex}-${paramIndex}`;
    variablePopupVisible.value[key] = false;
  }
};

// 获取所有可选择的变量（包括输入变量、步骤输出变量等）- 函数参数可以选择所有节点
const getAllVariablesForSelection = (currentStepIndex: number) => {
  // 直接返回变量树数据，支持选择所有节点（包括容器节点和叶子节点）
  const treeData = getVariableTreeData();

  // 递归展开所有节点，使其都可以被选择
  const flattenTreeData = (nodes: any[]): any[] => {
    const result: any[] = [];

    nodes.forEach((node) => {
      // 添加当前节点
      result.push({
        id: node.id,
        key: node.key,
        path: node.path,
        description: node.description,
        type: node.type,
        value: node.value,
        children: node.children || [],
      });

      // 递归添加子节点
      if (node.children && node.children.length > 0) {
        result.push(...flattenTreeData(node.children));
      }
    });

    return result;
  };

  const allVariables = flattenTreeData(treeData);

  // 添加前面步骤的输出变量
  for (let i = 0; i < currentStepIndex; i++) {
    const step = steps.value[i];
    if (step.outputVariable && step.outputVariable.trim()) {
      allVariables.push({
        id: `step_${step.id}`,
        key: step.outputVariable,
        path: step.outputVariable,
        description: `步骤${i + 1}输出: ${step.displayName}`,
        type: 'object',
        value: {
          type: 'variable' as const,
          variableType: 'current' as const,
        },
        children: [],
      });
    }
  }

  return allVariables;
};

// 获取变量树数据 - 支持树状结构，所有变量父节点都是ROOT节点
const getVariableTreeData = () => {
  // 创建ROOT节点结构
  const rootData: Record<string, any> = {};

  // 处理每个变量，支持嵌套路径，所有变量都在ROOT节点下
  variables.value.forEach((variable) => {
    const keys = variable.name.split('.');
    let current = rootData;

    // 构建嵌套结构
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!current[key]) {
        current[key] = {
          children: {},
          isContainer: true,
        };
      }
      current = current[key].children;
    }

    // 设置最终节点
    const finalKey = keys[keys.length - 1];
    current[finalKey] = {
      id: variable.id,
      key: finalKey,
      path: variable.name,
      description: variable.description || variable.name,
      type: variable.type,
      value: {
        type: 'variable' as const,
        variableType: variable.source === 'global' ? ('global' as const) : ('current' as const),
        variableValue: variable.value,
        variableName: variable.name,
      },
      children: [],
      isContainer: false,
    };
  });

  // 转换为树形结构，包装在ROOT节点下
  const convertToTreeData = (obj: Record<string, any>, parentPath = ''): any[] => {
    return Object.entries(obj).map(([key, value]) => {
      const currentPath = parentPath ? `${parentPath}.${key}` : key;

      if (value.isContainer) {
        // 容器节点
        return {
          id: `container_${currentPath}`,
          key,
          path: currentPath,
          description: key,
          type: 'object',
          value: {
            type: 'variable' as const,
            variableType: 'current' as const,
          },
          children: convertToTreeData(value.children, currentPath),
        };
      } else {
        // 叶子节点（实际变量）
        return value;
      }
    });
  };

  // 返回ROOT节点包装的数据
  if (Object.keys(rootData).length === 0) {
    return [];
  }

  // 始终返回根节点，即使没有变量也要显示根节点
  return [
    {
      id: 'ROOT',
      key: 'ROOT',
      path: 'ROOT',
      description: variables.value.length === 0 ? '根节点（暂无变量）' : '根节点',
      type: 'object',
      value: {
        type: 'variable' as const,
        variableType: 'current' as const,
      },
      children: convertToTreeData(rootData),
    },
  ];
};

// 变量树点击事件
const onVariableTreeClick = (data: any) => {
  console.log('变量树点击:', data);
};

// 变量树双击事件
const onVariableTreeDblClick = (data: any) => {
  console.log('变量树双击:', data);
  // 可以在这里实现快速赋值功能
};

// 测试值相关方法
// 从变量生成测试值JSON
const generateTestValuesFromVariables = () => {
  const testData: Record<string, any> = {};

  variables.value.forEach((variable) => {
    const keys = variable.name.split('.');
    let current = testData;

    // 构建嵌套结构
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!current[key]) {
        current[key] = {};
      }
      current = current[key];
    }

    // 设置最终值
    const finalKey = keys[keys.length - 1];
    current[finalKey] = variable.value || getDefaultValueByType(variable.type);
  });

  // 包装在ROOT节点下
  const rootData = {
    ROOT: testData,
  };

  testValues.value = rootData;
  testValuesJson.value = JSON.stringify(rootData, null, 2);
};

// 清空测试值
const clearTestValues = () => {
  testValues.value = {};
  testValuesJson.value = '{}';
};

// 处理设置测试值
const handleSetTestValues = () => {
  try {
    const parsedValues = JSON.parse(testValuesJson.value);
    testValues.value = parsedValues;

    // 更新变量列表中的测试值
    updateVariablesWithTestValues(parsedValues);

    showTestValueDialog.value = false;
    MessagePlugin.success('测试值设置成功');
  } catch (error) {
    MessagePlugin.error('JSON格式错误，请检查输入');
  }
};

// 更新变量列表中的测试值
const updateVariablesWithTestValues = (testData: Record<string, any>) => {
  const flattenObject = (obj: any, prefix = ''): Record<string, any> => {
    const result: Record<string, any> = {};

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = prefix ? `${prefix}.${key}` : key;

        if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
          Object.assign(result, flattenObject(obj[key], newKey));
        } else {
          result[newKey] = obj[key];
        }
      }
    }

    return result;
  };

  // 如果测试数据有ROOT节点，提取ROOT节点下的数据
  const actualData = testData.ROOT || testData;
  const flattenedData = flattenObject(actualData);

  // 更新现有变量的值
  variables.value.forEach((variable) => {
    if (flattenedData.hasOwnProperty(variable.name)) {
      variable.value = flattenedData[variable.name];
    }
  });
};
</script>

<style scoped>
.visual-function-composer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.composer-header {
  padding: 16px;
  border-bottom: 1px solid var(--td-border-level-1-color);
  background: var(--td-bg-color-container);
}

.step-count {
  color: var(--td-text-color-secondary);
  font-size: 14px;
}

.composer-body {
  flex: 1;
  display: flex;
  min-height: 0;
  overflow: hidden; /* 防止内容溢出 */
}

.variable-panel,
.output-panel {
  width: 300px;
  min-width: 280px; /* 设置最小宽度 */
  max-width: 400px; /* 设置最大宽度 */
  border-right: 1px solid var(--td-border-level-1-color);
  display: flex;
  flex-direction: column;
  flex-shrink: 0; /* 防止被压缩 */
}

.output-panel {
  border-right: none;
  border-left: 1px solid var(--td-border-level-1-color);
}

.composition-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 400px; /* 设置中间面板最小宽度 */
  overflow: hidden; /* 防止内容溢出 */
}

.panel-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--td-border-level-1-color);
  background: var(--td-bg-color-container);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--td-text-color-placeholder);
}

.empty-state p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.data-preview {
  background: var(--td-bg-color-code);
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  overflow: auto;
  max-height: 400px;
}

.function-steps {
  min-height: 200px;
}

.function-step {
  margin-bottom: 16px;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 8px;
  background: var(--td-bg-color-container);
  transition: all 0.2s;
  cursor: move;
}

.function-step.dragging {
  opacity: 0.5;
  transform: scale(0.95);
}

.function-step.drag-over {
  border-color: var(--td-brand-color);
  box-shadow: 0 0 0 2px rgba(var(--td-brand-color-rgb), 0.2);
  transform: translateY(-2px);
}

.function-step:hover {
  border-color: var(--td-brand-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.function-step.active {
  border-color: var(--td-brand-color);
  box-shadow: 0 0 0 2px rgba(var(--td-brand-color-rgb), 0.2);
  background: var(--td-brand-color-1);
}

.function-step:last-child {
  animation: highlight-new-step 1s ease-out;
}

@keyframes highlight-new-step {
  0% {
    background: var(--td-success-color-1);
    transform: scale(1.02);
  }
  100% {
    background: var(--td-bg-color-container);
    transform: scale(1);
  }
}

.step-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--td-brand-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  margin-right: 12px;
}

.step-info {
  flex: 1;
}

.step-name {
  font-weight: 500;
  margin-bottom: 2px;
}

.step-function {
  font-size: 12px;
  color: var(--td-text-color-secondary);
}

.step-actions {
  display: flex;
  gap: 4px;
}

.step-config {
  border-top: 1px solid var(--td-border-level-1-color);
  padding: 16px;
}

.config-section {
  margin-bottom: 16px;
}

.config-section:last-child {
  margin-bottom: 0;
}

.config-section h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
}

.param-item {
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.param-item.param-error {
  background-color: var(--td-error-color-1);
  border: 1px solid var(--td-error-color-3);
}

.param-item.param-required {
  background-color: var(--td-warning-color-1);
  border: 1px solid var(--td-warning-color-3);
}

.param-item label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: var(--td-text-color-secondary);
}

.param-item.param-error label {
  color: var(--td-error-color);
  font-weight: 500;
}

.param-item.param-required label {
  color: var(--td-warning-color);
  font-weight: 500;
}

.param-input {
  display: flex;
  gap: 8px;
}

.param-input > * {
  flex: 1;
}

/* 变量列表样式 */
.variable-list {
  max-height: 400px;
  overflow-y: auto;
}

.variable-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  margin-bottom: 8px;
  background: var(--td-bg-color-container);
  transition: all 0.2s;
}

.variable-item:hover {
  border-color: var(--td-brand-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.variable-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.variable-name {
  font-weight: 500;
  color: var(--td-text-color-primary);
  font-size: 13px;
}

.variable-type {
  font-size: 11px;
  color: var(--td-text-color-secondary);
  background: var(--td-bg-color-tag);
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
  width: fit-content;
}

.variable-source {
  font-size: 11px;
  color: var(--td-brand-color);
  background: var(--td-brand-color-1);
  padding: 2px 6px;
  border-radius: 3px;
  display: inline-block;
  width: fit-content;
}

.variable-actions {
  display: flex;
  gap: 4px;
}

/* 输出预览样式 */
.output-results {
  height: 100%;
  overflow-y: auto;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: var(--td-bg-color-container-select);
  border-radius: 6px;
  margin-bottom: 16px;
  font-weight: 500;
}

.execution-time {
  margin-left: auto;
  font-size: 12px;
  color: var(--td-text-color-secondary);
  background: var(--td-bg-color-tag);
  padding: 2px 6px;
  border-radius: 3px;
}

.step-results,
.final-result,
.generated-expression {
  margin-bottom: 16px;
}

.debug-info {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
}

.debug-info p {
  margin: 0 0 8px 0;
  font-weight: 500;
}

.debug-info pre {
  margin: 0;
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}

.step-results h5,
.final-result h5,
.generated-expression h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.step-result-item {
  /* margin-bottom: 12px; */
  /* border: 1px solid var(--td-border-level-1-color); */
  border-radius: 6px;
  overflow: hidden;
}

.step-result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--td-bg-color-container);
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.step-number {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--td-brand-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 500;
}

.step-name {
  font-weight: 500;
  color: var(--td-text-color-primary);
}

.step-time {
  margin-left: auto;
  font-size: 11px;
  color: var(--td-text-color-secondary);
}

.step-result-content,
.result-content,
.expression-content {
  padding: 4px 8px;
  background: var(--td-bg-color-code);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
}

.step-result-content pre,
.result-content pre,
.expression-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.error-result .error-content {
  padding: 12px;
  background: var(--td-error-color-1);
  border: 1px solid var(--td-error-color-3);
  border-radius: 6px;
  color: var(--td-error-color-7);
}

/* 变量选择器样式 */
.variable-selector {
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  overflow: hidden;
  background: var(--td-bg-color-container);
  width: 100%;

  :deep(.t-tabs__header) {
    background: var(--td-bg-color-container-select);
    margin: 0;
    padding: 0 16px;
  }

  :deep(.t-tabs__content) {
    padding: 16px;
    max-height: 300px;
    overflow-y: auto;
  }
}

.value-input-container {
  width: 100%;
  .t-radio-group {
    margin-bottom: 8px;
  }
}

/* 函数选择对话框样式 */
:deep(.t-dialog__body) {
  padding: 0;
  overflow: hidden;
}

:deep(.enhanced-function-list) {
  height: 650px;
  max-height: none;
}

/* Icon 样式优化 */
.t-button .t-icon {
  margin-right: 4px;
}

.t-button[size='small'] .t-icon {
  margin-right: 2px;
}

.empty-state .t-icon {
  margin-bottom: 8px;
  opacity: 0.6;
}

/* 步骤编号样式优化 */
.step-number {
  flex-shrink: 0;
}

/* 按钮组样式优化 */
.step-actions {
  opacity: 0.7;
  transition: opacity 0.2s;
}

.function-step:hover .step-actions {
  opacity: 1;
}

.variable-actions {
  opacity: 0.7;
  transition: opacity 0.2s;
}

.variable-item:hover .variable-actions {
  opacity: 1;
}

/* 变量树容器样式 */
.variable-tree-container {
  height: 100%;
  overflow-y: auto;
}

/* 变量树弹窗样式 */
.variable-tree-popup {
  width: 300px;
  max-height: 400px;
  overflow-y: auto;
  padding: 8px;
  background: var(--td-bg-color-container);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 测试值对话框样式 */
.test-values-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.test-values-header {
  padding: 12px 0;
  border-bottom: 1px solid var(--td-border-level-1-color);
  margin-bottom: 16px;
}

.test-values-content {
  flex: 1;
  min-height: 0;
}
</style>
