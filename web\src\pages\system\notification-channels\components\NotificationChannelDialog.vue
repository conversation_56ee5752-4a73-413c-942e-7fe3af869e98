<template>
  <t-dialog
    v-model:visible="dialogVisible"
    :title="isEdit ? '编辑通知通道' : '新增通知通道'"
    width="800px"
    :confirm-btn="null"
    :cancel-btn="null"
  >
    <t-form ref="formRef" :data="formData" :rules="formRules" label-width="120px" @submit="handleSubmit">
      <t-form-item label="通道名称" name="channelName">
        <t-input v-model="formData.channelName" placeholder="请输入通道名称" />
      </t-form-item>

      <t-form-item label="通道类型" name="channelType">
        <t-select
          v-model="formData.channelType"
          :options="channelTypes"
          placeholder="请选择通道类型"
          @change="handleChannelTypeChange"
        >
        </t-select>
      </t-form-item>

      <t-form-item label="描述" name="description">
        <t-textarea v-model="formData.description" placeholder="请输入描述" :maxlength="500" />
      </t-form-item>

      <t-form-item label="是否启用" name="isEnabled">
        <t-switch v-model="formData.isEnabled" />
      </t-form-item>

      <t-form-item label="发送间隔(秒)" name="intervalSeconds">
        <t-input-number v-model="formData.intervalSeconds" :min="1" :max="3600" />
      </t-form-item>

      <!-- 邮件配置 -->
      <template v-if="formData.channelType === 'Email'">
        <t-divider>邮件配置</t-divider>
        <email-config v-model="emailConfig" />
      </template>

      <!-- 钉钉配置 -->
      <template v-if="formData.channelType === 'Dingtalk'">
        <t-divider>钉钉配置</t-divider>
        <dingtalk-config v-model="dingtalkConfig" />
      </template>

      <!-- 飞书配置 -->
      <template v-if="formData.channelType === 'Feishu'">
        <t-divider>飞书配置</t-divider>
        <feishu-config v-model="feishuConfig" />
      </template>

      <!-- 企业微信配置 -->
      <template v-if="formData.channelType === 'Weixin'">
        <t-divider>企业微信配置</t-divider>
        <weixin-config v-model="weixinConfig" />
      </template>
    </t-form>

    <template #footer>
      <t-button theme="default" @click="handleCancel">取消</t-button>
      <t-button theme="primary" @click="handleSubmit">确定</t-button>
    </template>
  </t-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { api, Services } from '@/api/system';
import EmailConfig from './EmailConfig.vue';
import DingtalkConfig from './DingtalkConfig.vue';
import FeishuConfig from './FeishuConfig.vue';
import WeixinConfig from './WeixinConfig.vue';

// Props
const props = defineProps<{
  visible: boolean;
  channelData?: any;
  channelTypes: any[];
}>();

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean];
  success: [];
}>();

// 响应式数据
const formRef = ref();
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
});

const isEdit = computed(() => !!props.channelData?.id);

// 表单数据
const formData = reactive({
  id: '',
  channelName: '',
  channelType: '',
  description: '',
  isEnabled: true,
  intervalSeconds: 10,
});

// 各种配置对象
const emailConfig = ref({
  host: '',
  port: 465,
  fromName: '',
  fromAddress: '',
  password: '',
  toAddress: [],
  enableSsl: true,
});

const dingtalkConfig = ref({
  notificationType: 'robot',
  // 群机器人配置
  webHook: '',
  secret: '',
  isAtAll: false,
  atMobiles: [],
  // 工作通知配置
  appKey: '',
  appSecret: '',
  agentId: 0,
  userIds: [],
  deptIds: [],
  toAllUser: false,
});

const feishuConfig = ref({
  webHook: '',
  secret: '',
});

const weixinConfig = ref({
  webHook: '',
  mentionedList: [],
  mentionedMobileList: [],
});

// 表单验证规则
const formRules = {
  channelName: [{ required: true, message: '请输入通道名称', trigger: 'blur' }],
  channelType: [{ required: true, message: '请选择通道类型', trigger: 'change' }],
  intervalSeconds: [{ required: true, message: '请输入发送间隔', trigger: 'blur' }],
};

// 监听数据变化
watch(
  () => props.channelData,
  (newData) => {
    if (newData) {
      Object.assign(formData, newData);

      // 解析配置JSON
      if (newData.configJson) {
        try {
          const config = JSON.parse(newData.configJson);
          switch (newData.channelType) {
            case 'Email':
              emailConfig.value = { ...emailConfig.value, ...config };
              break;
            case 'Dingtalk':
              dingtalkConfig.value = { ...dingtalkConfig.value, ...config };
              break;
            case 'Feishu':
              feishuConfig.value = { ...feishuConfig.value, ...config };
              break;
            case 'Weixin':
              weixinConfig.value = { ...weixinConfig.value, ...config };
              break;
          }
        } catch (error) {
          console.error('解析配置JSON失败:', error);
        }
      }
    } else {
      // 重置表单
      Object.assign(formData, {
        id: '',
        channelName: '',
        channelType: '',
        description: '',
        isEnabled: true,
        intervalSeconds: 10,
      });

      // 重置配置
      emailConfig.value = {
        host: '',
        port: 465,
        fromName: '',
        fromAddress: '',
        password: '',
        toAddress: [],
        enableSsl: true,
      };

      dingtalkConfig.value = {
        notificationType: 'robot',
        // 群机器人配置
        webHook: '',
        secret: '',
        isAtAll: false,
        atMobiles: [],
        // 工作通知配置
        appKey: '',
        appSecret: '',
        agentId: 0,
        userIds: [],
        deptIds: [],
        toAllUser: false,
      };

      feishuConfig.value = {
        webHook: '',
        secret: '',
      };

      weixinConfig.value = {
        webHook: '',
        mentionedList: [],
        mentionedMobileList: [],
      };
    }
  },
  { immediate: true },
);

// 方法
const handleChannelTypeChange = () => {
  // 通道类型改变时重置配置
  emailConfig.value = {
    host: '',
    port: 465,
    fromName: '',
    fromAddress: '',
    password: '',
    toAddress: [],
    enableSsl: true,
  };

  dingtalkConfig.value = {
    notificationType: 'robot',
    // 群机器人配置
    webHook: '',
    secret: '',
    isAtAll: false,
    atMobiles: [],
    // 工作通知配置
    appKey: '',
    appSecret: '',
    agentId: 0,
    userIds: [],
    deptIds: [],
    toAllUser: false,
  };

  feishuConfig.value = {
    webHook: '',
    secret: '',
  };

  weixinConfig.value = {
    webHook: '',
    mentionedList: [],
    mentionedMobileList: [],
  };
};

const getConfigJson = () => {
  switch (formData.channelType) {
    case 'Email':
      return JSON.stringify(emailConfig.value);
    case 'Dingtalk':
      return JSON.stringify(dingtalkConfig.value);
    case 'Feishu':
      return JSON.stringify(feishuConfig.value);
    case 'Weixin':
      return JSON.stringify(weixinConfig.value);
    default:
      return '{}';
  }
};

const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate();
    if (!valid) return;

    const submitData = {
      ...formData,
      configJson: getConfigJson(),
    };

    await api.run(Services.notificationChannelSave, submitData);
    MessagePlugin.success(isEdit.value ? '更新成功' : '创建成功');
    emit('success');
  } catch (error) {
    console.error('保存失败:', error);
    MessagePlugin.error('保存失败');
  }
};

const handleCancel = () => {
  dialogVisible.value = false;
};
</script>

<style scoped>
:deep(.t-form-item) {
  margin-bottom: 20px;
}
</style>
