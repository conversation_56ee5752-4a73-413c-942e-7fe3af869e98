import { FlowData, FlowDataValue } from '@/components/action-panel/model';

export interface ArgsInfo {
  name: string;
  description: string;

  // TCP连接配置
  host: FlowDataValue;
  port: FlowDataValue;

  // 消息配置
  message: FlowDataValue;
  messageFormat: 'text' | 'json' | 'hex'; // 消息格式

  // 连接配置
  timeout: FlowDataValue; // 超时时间（毫秒）
  encoding: 'utf8' | 'ascii' | 'base64'; // 编码格式

  // 是否等待响应
  waitForResponse: boolean;
  responseTimeout: FlowDataValue; // 响应超时时间（毫秒）
}
