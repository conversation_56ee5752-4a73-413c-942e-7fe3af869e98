import { ref, computed, onUnmounted, watch, type Ref } from 'vue';
import { FlowData, FlowDataValue, VariableReference } from '../model';
import { variableReferenceManager } from '../utils/variableReferenceManager';
import { getRandomId } from '../utils';

/**
 * 变量引用管理的组合式函数
 * 提供变量引用的注册、解析和同步功能
 */
export function useVariableReference(dataValue: Ref<FlowDataValue>, availableVariables?: Ref<FlowData[]>) {
  const valueId = ref(getRandomId());
  const variableReference = ref<VariableReference | undefined>();

  /**
   * 注册变量引用
   * @param variableData 变量数据
   */
  const registerReference = (variableData: FlowData) => {
    if (dataValue.value?.type === 'variable' && variableData) {
      variableReferenceManager.registerReference(valueId.value, dataValue.value, variableData);
      updateVariableReference();
    }
  };

  /**
   * 更新变量引用信息
   */
  const updateVariableReference = () => {
    variableReference.value = variableReferenceManager.getVariableReferenceByValue(
      dataValue.value,
      availableVariables?.value,
    );
  };

  /**
   * 获取实时的变量信息
   */
  const resolvedVariableInfo = computed(() => {
    if (dataValue.value?.type !== 'variable') return null;

    const reference = variableReference.value;
    if (!reference) return null;

    return {
      id: reference.id,
      path: reference.path,
      type: reference.type,
      name: reference.name,
      description: reference.description,
      source: reference.source,
      displayText: reference.name || reference.path,
    };
  });

  /**
   * 检查变量引用是否有效
   */
  const isValidReference = computed(() => {
    return dataValue.value?.type === 'variable' && !!variableReference.value;
  });

  /**
   * 获取变量显示文本
   */
  const variableDisplayText = computed(() => {
    if (dataValue.value?.type !== 'variable') return '';

    const resolved = resolvedVariableInfo.value;
    if (resolved) {
      return resolved.displayText;
    }

    // 降级到原有的显示方式
    return dataValue.value?.variableName || dataValue.value?.variableValue || '未选择变量';
  });

  /**
   * 获取变量类型
   */
  const variableType = computed(() => {
    const resolved = resolvedVariableInfo.value;
    return resolved?.type || dataValue.value?.dataType || '';
  });

  /**
   * 变更监听器
   */
  const handleVariableChange = (variableId: string, reference: VariableReference) => {
    if (dataValue.value?.variableRefId === variableId) {
      updateVariableReference();
    }
  };

  // 监听数据值变化
  watch(
    () => dataValue.value,
    (newValue) => {
      updateVariableReference();
    },
    { deep: true },
  );

  // 监听可用变量列表变化
  watch(
    () => availableVariables?.value,
    (newVariables) => {
      updateVariableReference();
    },
    { deep: true },
  );

  // 初始化时尝试恢复变量引用
  if (dataValue.value?.type === 'variable' && dataValue.value.variableRefId) {
    updateVariableReference();
  }

  // 注册变更监听器
  variableReferenceManager.addChangeListener(handleVariableChange);

  // 组件卸载时清理
  onUnmounted(() => {
    variableReferenceManager.unregisterReference(valueId.value);
    variableReferenceManager.removeChangeListener(handleVariableChange);
  });

  return {
    valueId: valueId.value,
    variableReference,
    resolvedVariableInfo,
    isValidReference,
    variableDisplayText,
    variableType,
    registerReference,
    updateVariableReference,
  };
}

/**
 * 变量选择的组合式函数
 * 用于处理变量选择对话框中的双击事件
 */
export function useVariableSelection() {
  /**
   * 处理变量双击选择
   * @param dataValue 数据值对象
   * @param path 变量路径
   * @param item 变量项数据
   * @param variableType 变量类型（current/local/global）
   */
  const handleVariableSelect = (
    dataValue: FlowDataValue,
    path: string,
    item: { data: FlowData },
    variableType: 'current' | 'local' | 'global',
  ) => {
    if (dataValue.type !== 'variable') return;

    const itemData: FlowData = item.data;

    // 创建一个用于注册的变量数据副本，确保路径信息正确
    const variableDataForRegistration: FlowData = {
      ...itemData,
      // 使用传入的path作为变量路径，这个path已经是处理过的（去掉ROOT的）
      path: path,
      // 如果原始数据的path包含ROOT，也要去掉ROOT前缀
      key: itemData.key === 'ROOT' ? path : itemData.key,
    };

    // 设置基本信息
    dataValue.variableType = variableType;
    dataValue.dataType = itemData.type;
    dataValue.variableValue = path;
    dataValue.variableName = itemData.pathDescription || itemData.description;

    // 注册变量引用，使用处理过的变量数据
    const valueId = getRandomId();
    variableReferenceManager.registerReference(valueId, dataValue, variableDataForRegistration);
  };

  return {
    handleVariableSelect,
  };
}

/**
 * 变量管理的组合式函数
 * 用于处理变量的增删改操作
 */
export function useVariableManagement() {
  /**
   * 更新变量
   * @param oldVariableData 旧的变量数据
   * @param newVariableData 新的变量数据
   */
  const updateVariable = (oldVariableData: FlowData, newVariableData: FlowData) => {
    variableReferenceManager.updateVariable(oldVariableData, newVariableData);
  };

  /**
   * 删除变量
   * @param variableData 变量数据
   */
  const deleteVariable = (variableData: FlowData) => {
    variableReferenceManager.deleteVariable(variableData);
  };

  /**
   * 批量更新变量
   * @param updates 更新列表
   */
  const batchUpdateVariables = (updates: Array<{ old: FlowData; new: FlowData }>) => {
    updates.forEach(({ old, new: newData }) => {
      updateVariable(old, newData);
    });
  };

  return {
    updateVariable,
    deleteVariable,
    batchUpdateVariables,
  };
}
