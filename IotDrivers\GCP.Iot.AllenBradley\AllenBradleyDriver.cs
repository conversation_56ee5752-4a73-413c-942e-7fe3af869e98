using GCP.Iot.Interfaces;
using GCP.Iot.Models;
using IoTClient.Clients.PLC;
using Serilog;
using DataTypeEnum = GCP.Iot.Models.DataTypeEnum;
using IotDataTypeEnum = IoTClient.Enums.DataTypeEnum;

namespace GCP.Iot.AllenBradley
{
    [DriverInfo("提供与Allen-Bradley（罗克韦尔）PLC的连接。支持ControlLogix、CompactLogix等系列PLC。")]
    public class AllenBradleyDriver : IDriver
    {
        private AllenBradleyClient _client;

        public AllenBradleyDriver()
        {
        }

        public bool IsConnected => _client is { Connected: true };

        private ILogger _logger;
        public ILogger Logger
        {
            get => _logger;
            set
            {
                _logger = value;
                _logger?.Information($"AllenBradley驱动初始化完成");
            }
        }

        public string DriverCode => "AllenBradley";
        public bool SupportsBatchReading => true;

        #region 配置参数

        [DriverParameter("最小采集周期(毫秒)")]
        public int MinSamplingPeriod { get; set; } = 1000;

        [DriverParameter("IP地址")]
        public string IpAddress { get; set; } = "*************";

        [DriverParameter("端口号")]
        public int Port { get; set; } = 44818;

        [DriverParameter("超时时间ms")]
        public int Timeout { get; set; } = 3000;

        [DriverParameter("存档周期(毫秒)")]
        public int? ArchivePeriod { get; set; }

        #endregion

        public Task<bool> ConnectAsync()
        {
            try
            {
                _client?.Close();

                _client = new AllenBradleyClient(IpAddress, Port, timeout: Timeout);

                var result = _client.Open();
                if (!result.IsSucceed)
                {
                    throw result.Exception;
                }

                Logger?.Information("AllenBradley驱动连接成功: {IpAddress}:{Port}", IpAddress, Port);
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                Logger?.Error(ex, "AllenBradley驱动连接失败: {IpAddress}:{Port}", IpAddress, Port);
                return Task.FromResult(false);
            }
        }

        public Task<bool> DisconnectAsync()
        {
            try
            {
                _client?.Close();
                Logger?.Information("AllenBradley驱动断开连接：{IpAddress}:{Port}", IpAddress, Port);
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                Logger?.Error(ex, "AllenBradley驱动断开连接失败：{IpAddress}:{Port}", IpAddress, Port);
                return Task.FromResult(false);
            }
        }

        [DriverMethod("读AllenBradley", description: "读AllenBradley地址")]
        public Task<DriverOperationResult> ReadAsync(string address, DataTypeEnum dataType)
        {
            var result = new DriverOperationResult
            {
                Status = OperationStatus.Success,
                VariableId = address,
            };

            try
            {
                if (!IsConnected)
                {
                    result.Status = OperationStatus.NotConnected;
                    result.ErrorMessage = "设备未连接";
                    return Task.FromResult(result);
                }

                dynamic iotResult = null;

                switch (dataType)
                {
                    case DataTypeEnum.Bool:
                        iotResult = _client.ReadBoolean(address);
                        break;
                    case DataTypeEnum.Byte:
                        iotResult = _client.ReadByte(address);
                        break;
                    case DataTypeEnum.UByte:
                        iotResult = _client.ReadByte(address);
                        break;
                    case DataTypeEnum.Int16:
                        iotResult = _client.ReadInt16(address);
                        break;
                    case DataTypeEnum.Uint16:
                        iotResult = _client.ReadUInt16(address);
                        break;
                    case DataTypeEnum.Int32:
                        iotResult = _client.ReadInt32(address);
                        break;
                    case DataTypeEnum.Uint32:
                        iotResult = _client.ReadUInt32(address);
                        break;
                    case DataTypeEnum.Int64:
                        iotResult = _client.ReadInt64(address);
                        break;
                    case DataTypeEnum.Uint64:
                        iotResult = _client.ReadUInt64(address);
                        break;
                    case DataTypeEnum.Float:
                        iotResult = _client.ReadFloat(address);
                        break;
                    case DataTypeEnum.Double:
                        iotResult = _client.ReadDouble(address);
                        break;
                    case DataTypeEnum.AsciiString:
                    case DataTypeEnum.Utf8String:
                    case DataTypeEnum.Gb2312String:
                        iotResult = _client.ReadString(address);
                        break;
                    default:
                        throw new ArgumentException($"不支持的数据类型: {dataType}");
                }

                if (!iotResult.IsSucceed)
                {
                    throw iotResult.Exception;
                }

                result.RawValue = iotResult.Value;
                result.ProcessedValue = iotResult.Value;
                result.Timestamp = DateTime.Now;
            }
            catch (Exception ex)
            {
                result.Status = OperationStatus.Failed;
                result.ErrorMessage = ex.Message;
                Logger?.Error(ex, $"读取AllenBradley地址失败: {address}");
            }

            return Task.FromResult(result);
        }

        public async Task<DriverOperationResult> BatchReadAsync(Dictionary<string, DataTypeEnum> addresses)
        {
            var result = new DriverOperationResult
            {
                Status = OperationStatus.Success,
                Timestamp = DateTime.Now
            };

            try
            {
                if (!IsConnected)
                {
                    result.Status = OperationStatus.NotConnected;
                    result.ErrorMessage = "设备未连接";
                    return result;
                }

                // 分离字符串类型和非字符串类型的地址
                var numAddresses = addresses
                    .Where(t => t.Value is not DataTypeEnum.AsciiString and not DataTypeEnum.Utf8String
                        and not DataTypeEnum.Gb2312String)
                    .ToDictionary(kvp => kvp.Key, kvp => ConvertToIotDataType(kvp.Value));

                var values = new Dictionary<string, object>();

                // 批量读取非字符串类型
                if (numAddresses.Count > 0)
                {
                    var readResult = _client.BatchRead(numAddresses, 100);

                    if (!readResult.IsSucceed)
                        throw readResult.Exception;

                    foreach (var item in readResult.Value)
                    {
                        values.Add(item.Key, item.Value);
                    }
                }

                // 独立读取字符串类型
                var stringTasks = addresses
                    .Where(t => t.Value is DataTypeEnum.AsciiString or DataTypeEnum.Utf8String or DataTypeEnum.Gb2312String)
                    .Select(t => ReadAsync(t.Key, t.Value))
                    .ToList();

                await Task.WhenAll(stringTasks);

                foreach (var task in stringTasks)
                {
                    values.Add(task.Result.VariableId, task.Result.RawValue);
                }

                result.RawValue = values;
                result.ProcessedValue = values;
            }
            catch (Exception ex)
            {
                result.Status = OperationStatus.Failed;
                result.ErrorMessage = ex.Message;
                Logger?.Error(ex, "批量读取AllenBradley地址失败");
            }

            return result;
        }

        [DriverMethod("写AllenBradley", description: "写AllenBradley地址")]
        public Task<DriverOperationResult> WriteAsync(string address, object value, DataTypeEnum dataType)
        {
            var result = new DriverOperationResult
            {
                Status = OperationStatus.Success,
                VariableId = address,
                Timestamp = DateTime.Now
            };

            try
            {
                if (!IsConnected)
                {
                    result.Status = OperationStatus.NotConnected;
                    result.ErrorMessage = "设备未连接";
                    return Task.FromResult(result);
                }

                var iotDataType = ConvertToIotDataType(dataType);
                var writeResult = _client.Write(address, value, iotDataType);

                if (!writeResult.IsSucceed)
                {
                    throw writeResult.Exception;
                }

                result.RawValue = value;
                result.ProcessedValue = value;
            }
            catch (Exception ex)
            {
                result.Status = OperationStatus.Failed;
                result.ErrorMessage = ex.Message;
                Logger?.Error(ex, $"写入AllenBradley地址失败: {address}");
            }

            return Task.FromResult(result);
        }



        private IotDataTypeEnum ConvertToIotDataType(DataTypeEnum dataType)
        {
            return dataType switch
            {
                DataTypeEnum.Bool => IotDataTypeEnum.Bool,
                DataTypeEnum.Byte => IotDataTypeEnum.Byte,
                DataTypeEnum.UByte => IotDataTypeEnum.Byte,
                DataTypeEnum.Int16 => IotDataTypeEnum.Int16,
                DataTypeEnum.Uint16 => IotDataTypeEnum.UInt16,
                DataTypeEnum.Int32 => IotDataTypeEnum.Int32,
                DataTypeEnum.Uint32 => IotDataTypeEnum.UInt32,
                DataTypeEnum.Int64 => IotDataTypeEnum.Int64,
                DataTypeEnum.Uint64 => IotDataTypeEnum.UInt64,
                DataTypeEnum.Float => IotDataTypeEnum.Float,
                DataTypeEnum.Double => IotDataTypeEnum.Double,
                DataTypeEnum.AsciiString => IotDataTypeEnum.String,
                DataTypeEnum.Utf8String => IotDataTypeEnum.String,
                DataTypeEnum.Gb2312String => IotDataTypeEnum.String,
                _ => throw new ArgumentException($"不支持的数据类型: {dataType}")
            };
        }

        public void Dispose()
        {
            try
            {
                _client?.Close();
                Logger?.Information("AllenBradley驱动已释放资源");
            }
            catch (Exception ex)
            {
                Logger?.Error(ex, "释放AllenBradley驱动资源时发生错误");
            }
        }
    }
}
