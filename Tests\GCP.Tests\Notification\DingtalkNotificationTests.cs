using System.Text.Json;
using GCP.Models;
using Xunit;

namespace GCP.Tests.Notification
{
    public class DingtalkNotificationTests
    {
        [Fact]
        public void DingtalkRobotConfig_SerializeDeserialize_ShouldWork()
        {
            // Arrange
            var config = new DingtalkNotificationConfig
            {
                NotificationType = "robot",
                WebHook = "https://oapi.dingtalk.com/robot/send?access_token=test",
                Secret = "test_secret",
                IsAtAll = true,
                AtMobiles = new List<string> { "13800138000", "13800138001" }
            };

            // Act
            var json = JsonSerializer.Serialize(config);
            var deserializedConfig = JsonSerializer.Deserialize<DingtalkNotificationConfig>(json);

            // Assert
            Assert.NotNull(deserializedConfig);
            Assert.Equal("robot", deserializedConfig.NotificationType);
            Assert.Equal(config.WebHook, deserializedConfig.WebHook);
            Assert.Equal(config.Secret, deserializedConfig.Secret);
            Assert.Equal(config.IsAtAll, deserializedConfig.IsAtAll);
            Assert.Equal(2, deserializedConfig.AtMobiles.Count);
            Assert.Contains("13800138000", deserializedConfig.AtMobiles);
            Assert.Contains("13800138001", deserializedConfig.AtMobiles);
        }

        [Fact]
        public void DingtalkWorkNoticeConfig_SerializeDeserialize_ShouldWork()
        {
            // Arrange
            var config = new DingtalkNotificationConfig
            {
                NotificationType = "workNotice",
                AppKey = "test_app_key",
                AppSecret = "test_app_secret",
                AgentId = 123456789,
                UserIds = new List<string> { "user001", "user002" },
                DeptIds = new List<string> { "1", "2", "3" },
                ToAllUser = false
            };

            // Act
            var json = JsonSerializer.Serialize(config);
            var deserializedConfig = JsonSerializer.Deserialize<DingtalkNotificationConfig>(json);

            // Assert
            Assert.NotNull(deserializedConfig);
            Assert.Equal("workNotice", deserializedConfig.NotificationType);
            Assert.Equal(config.AppKey, deserializedConfig.AppKey);
            Assert.Equal(config.AppSecret, deserializedConfig.AppSecret);
            Assert.Equal(config.AgentId, deserializedConfig.AgentId);
            Assert.Equal(2, deserializedConfig.UserIds.Count);
            Assert.Contains("user001", deserializedConfig.UserIds);
            Assert.Contains("user002", deserializedConfig.UserIds);
            Assert.Equal(3, deserializedConfig.DeptIds.Count);
            Assert.Contains("1", deserializedConfig.DeptIds);
            Assert.Contains("2", deserializedConfig.DeptIds);
            Assert.Contains("3", deserializedConfig.DeptIds);
            Assert.False(deserializedConfig.ToAllUser);
        }

        [Fact]
        public void DingtalkConfig_DefaultValues_ShouldBeCorrect()
        {
            // Arrange & Act
            var config = new DingtalkNotificationConfig();

            // Assert
            Assert.Equal("robot", config.NotificationType);
            Assert.Equal(NotificationChannelType.Dingtalk, config.ChannelType);
            Assert.NotNull(config.AtMobiles);
            Assert.Empty(config.AtMobiles);
            Assert.NotNull(config.UserIds);
            Assert.Empty(config.UserIds);
            Assert.NotNull(config.DeptIds);
            Assert.Empty(config.DeptIds);
            Assert.False(config.IsAtAll);
            Assert.False(config.ToAllUser);
        }

        [Theory]
        [InlineData("robot")]
        [InlineData("workNotice")]
        public void DingtalkConfig_NotificationType_ShouldAcceptValidValues(string notificationType)
        {
            // Arrange & Act
            var config = new DingtalkNotificationConfig
            {
                NotificationType = notificationType
            };

            // Assert
            Assert.Equal(notificationType, config.NotificationType);
        }

        [Fact]
        public void DingtalkOptions_EasyNotice_ShouldSupportWorkNotice()
        {
            // Arrange
            var options = new EasyNotice.DingtalkOptions
            {
                NotificationType = "workNotice",
                AppKey = "test_app_key",
                AppSecret = "test_app_secret",
                AgentId = 123456789,
                UserIds = new List<string> { "user001" },
                DeptIds = new List<string> { "1" },
                ToAllUser = false
            };

            // Act & Assert
            Assert.Equal("workNotice", options.NotificationType);
            Assert.Equal("test_app_key", options.AppKey);
            Assert.Equal("test_app_secret", options.AppSecret);
            Assert.Equal(123456789, options.AgentId);
            Assert.Single(options.UserIds);
            Assert.Single(options.DeptIds);
            Assert.False(options.ToAllUser);
        }

        [Fact]
        public void DynamicNotificationConfig_SerializeDeserialize_ShouldWork()
        {
            // Arrange
            var dynamicConfig = new DynamicNotificationConfig
            {
                Dingtalk = new DingtalkDynamicConfig
                {
                    AdditionalUserIds = new List<string> { "user003", "user004" },
                    AdditionalDeptIds = new List<string> { "3", "4" },
                    AdditionalAtMobiles = new List<string> { "13800138002" },
                    OverrideIsAtAll = true,
                    OverrideToAllUser = false
                }
            };

            // Act
            var json = JsonSerializer.Serialize(dynamicConfig);
            var deserializedConfig = JsonSerializer.Deserialize<DynamicNotificationConfig>(json);

            // Assert
            Assert.NotNull(deserializedConfig);
            Assert.NotNull(deserializedConfig.Dingtalk);
            Assert.Equal(2, deserializedConfig.Dingtalk.AdditionalUserIds.Count);
            Assert.Contains("user003", deserializedConfig.Dingtalk.AdditionalUserIds);
            Assert.Contains("user004", deserializedConfig.Dingtalk.AdditionalUserIds);
            Assert.Equal(2, deserializedConfig.Dingtalk.AdditionalDeptIds.Count);
            Assert.Contains("3", deserializedConfig.Dingtalk.AdditionalDeptIds);
            Assert.Contains("4", deserializedConfig.Dingtalk.AdditionalDeptIds);
            Assert.Single(deserializedConfig.Dingtalk.AdditionalAtMobiles);
            Assert.Contains("13800138002", deserializedConfig.Dingtalk.AdditionalAtMobiles);
            Assert.True(deserializedConfig.Dingtalk.OverrideIsAtAll);
            Assert.False(deserializedConfig.Dingtalk.OverrideToAllUser);
        }

        [Fact]
        public void NotificationRequest_WithDynamicConfig_ShouldWork()
        {
            // Arrange
            var request = new NotificationRequest
            {
                Title = "测试通知",
                Content = "这是一条测试通知",
                DynamicConfig = new DynamicNotificationConfig
                {
                    Dingtalk = new DingtalkDynamicConfig
                    {
                        AdditionalUserIds = new List<string> { "dynamic_user001" },
                        AdditionalDeptIds = new List<string> { "dynamic_dept001" },
                        OverrideToAllUser = true
                    }
                }
            };

            // Act & Assert
            Assert.Equal("测试通知", request.Title);
            Assert.Equal("这是一条测试通知", request.Content);
            Assert.NotNull(request.DynamicConfig);
            Assert.NotNull(request.DynamicConfig.Dingtalk);
            Assert.Single(request.DynamicConfig.Dingtalk.AdditionalUserIds);
            Assert.Single(request.DynamicConfig.Dingtalk.AdditionalDeptIds);
            Assert.True(request.DynamicConfig.Dingtalk.OverrideToAllUser);
        }
    }
}
