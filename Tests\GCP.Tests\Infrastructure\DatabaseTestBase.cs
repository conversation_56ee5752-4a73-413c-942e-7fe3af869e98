using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit.Abstractions;
using GCP.DataAccess;
using GCP.FunctionPool;

namespace GCP.Tests.Infrastructure
{
    /// <summary>
    /// 数据库测试基类
    /// </summary>
    public abstract class DatabaseTestBase : TestBase
    {
        private TestDatabaseManager? _databaseManager;

        protected DatabaseTestBase(ITestOutputHelper output) : base(output)
        {
        }

        protected override void ConfigureTestServices(IServiceCollection services)
        {
            base.ConfigureTestServices(services);

            // 配置测试数据库连接
            ConfigureDatabaseServices(services);
        }

        protected override void ConfigureData()
        {
            base.ConfigureData();
            FunctionRunner.InitRun(false);
        }

        /// <summary>
        /// 配置数据库服务
        /// </summary>;

        protected virtual void ConfigureDatabaseServices(IServiceCollection services)
        {
            // 创建临时的logger用于数据库管理器
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var dbLogger = loggerFactory.CreateLogger<TestDatabaseManager>();
            _databaseManager = new TestDatabaseManager(Configuration, dbLogger);

            // 配置数据库
            services.AddTransient<GcpDb>(_ =>
            {
                var connectionString = _databaseManager.GetConnectionString();
                var providerName = _databaseManager.GetProviderName();
                return new GcpDb(providerName, connectionString);
            });

            // 配置流程数据库（使用同一个数据库）
            services.AddTransient<GcpFlowDb>(_ =>
            {
                var connectionString = _databaseManager.GetConnectionString();
                var providerName = _databaseManager.GetProviderName();
                return new GcpFlowDb(providerName, connectionString);
            });

            // 配置数据库上下文
            services.AddScoped<IDbContext>(_ =>
            {
                var connectionString = _databaseManager.GetConnectionString();
                var providerName = _databaseManager.GetProviderName();
                var dbProviderType = providerName == "MySql" ? DbProviderType.MySql : DbProviderType.SQLite;
                DbSettings.DbProviderType = dbProviderType;
                DbSettings.DefaultConnectionString = connectionString;
                return new DbContext(connectionString, dbProviderType);
            });

            // 运行数据库迁移
            RunDatabaseMigrations();

            // 初始化函数池
            InitializeFunctionPool();
        }

        /// <summary>
        /// 运行数据库迁移创建表结构
        /// </summary>
        private void RunDatabaseMigrations()
        {
            try
            {
                _databaseManager?.RunMigrationsAsync().GetAwaiter().GetResult();
                Output.WriteLine("数据库迁移完成");
            }
            catch (Exception ex)
            {
                Output.WriteLine($"数据库迁移失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 初始化函数池
        /// </summary>
        private void InitializeFunctionPool()
        {
            try
            {
                // 初始化FunctionRunner，注册本地和数据库函数
                FunctionRunner.Init(Configuration);
                Output.WriteLine("函数池初始化完成");
            }
            catch (Exception ex)
            {
                Output.WriteLine($"函数池初始化失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取主数据库实例
        /// </summary>
        protected GcpDb GetDatabase()
        {
            return GetService<GcpDb>();
        }

        /// <summary>
        /// 获取流程数据库实例
        /// </summary>
        protected GcpFlowDb GetFlowDatabase()
        {
            return GetService<GcpFlowDb>();
        }

        /// <summary>
        /// 初始化测试数据
        /// </summary>
        protected virtual async Task InitializeTestDataAsync()
        {
            using var db = GetDatabase();
            await CreateTestTablesAsync(db);
            await SeedTestDataAsync(db);
        }

        /// <summary>
        /// 创建测试表结构
        /// </summary>
        protected virtual async Task CreateTestTablesAsync(GcpDb db)
        {
            // 子类可以重写此方法来创建特定的测试表
            await Task.CompletedTask;
        }

        /// <summary>
        /// 填充测试数据
        /// </summary>
        protected virtual async Task SeedTestDataAsync(GcpDb db)
        {
            // 子类可以重写此方法来填充特定的测试数据
            await Task.CompletedTask;
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        protected virtual async Task CleanupTestDataAsync()
        {
            using var db = GetDatabase();
            await CleanupTablesAsync(db);
        }

        /// <summary>
        /// 清理测试表
        /// </summary>
        protected virtual async Task CleanupTablesAsync(GcpDb db)
        {
            // 子类可以重写此方法来清理特定的测试表
            await Task.CompletedTask;
        }

        public override void Dispose()
        {
            try
            {
                CleanupTestDataAsync().GetAwaiter().GetResult();

                // 清理测试数据库
                _databaseManager?.Dispose();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "清理测试数据时发生错误");
            }

            base.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
