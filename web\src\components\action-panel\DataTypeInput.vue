<template>
  <div class="data-type-input">
    <!-- 文本类型 -->
    <t-textarea
      v-if="isTextType"
      v-model="stringValue"
      :placeholder="placeholder"
      :autosize="{ minRows: 5, maxRows: 12 }"
      @change="onInputChange"
    />

    <!-- 数值类型 -->
    <t-input-number
      v-else-if="isNumberType"
      v-model="numberValue"
      :placeholder="placeholder"
      :decimal-places="getDecimalPlaces()"
      :min="getMinValue()"
      :max="getMaxValue()"
      @change="onInputChange"
    />

    <!-- 日期类型 -->
    <t-date-picker
      v-else-if="isDateType"
      v-model="dateValue"
      :placeholder="placeholder"
      format="YYYY-MM-DD"
      @change="onInputChange"
    />

    <!-- 日期时间类型 -->
    <t-date-picker
      v-else-if="isDateTimeType"
      v-model="dateValue"
      :placeholder="placeholder"
      enable-time-picker
      format="YYYY-MM-DD HH:mm:ss"
      @change="onInputChange"
    />

    <!-- 布尔值类型 -->
    <t-switch v-else-if="isBoolType" v-model="boolValue" @change="onInputChange" />

    <!-- 字符类型 -->
    <t-input
      v-else-if="isCharType"
      v-model="stringValue"
      :placeholder="placeholder"
      :maxlength="1"
      @change="onInputChange"
    />

    <!-- 二进制数据类型 -->
    <t-upload
      v-else-if="isBinaryType"
      v-model="fileList"
      :action="uploadAction"
      :before-upload="beforeUpload"
      :on-success="onUploadSuccess"
      :on-error="onUploadError"
      theme="file"
      :placeholder="placeholder || '选择文件'"
      :tips="'支持上传二进制文件'"
    />

    <!-- 对象/数组类型 -->
    <structured-editor
      v-else-if="isObjectOrArrayType"
      v-model="objectValue"
      :data-type="props.dataType as 'array' | 'object'"
      @change="onInputChange"
    />

    <!-- 默认文本输入 -->
    <t-textarea
      v-else
      v-model="stringValue"
      :placeholder="placeholder"
      :autosize="{ minRows: 5, maxRows: 12 }"
      @change="onInputChange"
    />
  </div>
</template>

<script lang="ts">
export default {
  name: 'DataTypeInput',
};
</script>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import StructuredEditor from './StructuredEditor.vue';

interface Props {
  modelValue?: string;
  dataType?: string;
  placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  dataType: 'string',
  placeholder: '请输入内容',
});

const emits = defineEmits(['update:modelValue', 'change']);

// 类型转换为字符串的辅助函数
const convertTypeToString = (value: any, dataType: string): string => {
  if (value === null || value === undefined) return '';

  switch (dataType) {
    case 'bool':
      return value ? 'true' : 'false';
    case 'object':
    case 'array':
      return typeof value === 'object' ? JSON.stringify(value) : String(value);
    default:
      return String(value);
  }
};

// 不同组件类型的内部值
const stringValue = ref('');
const numberValue = ref(0);
const boolValue = ref(false);
const dateValue = ref('');
const objectValue = ref({});
const fileList = ref([]);

const jsonString = ref('');
const jsonError = ref('');

// 注意：结构化编辑器相关代码已移至 StructuredEditor.vue 组件

// 类型判断
const isTextType = computed(() => {
  return ['string'].includes(props.dataType);
});

const isNumberType = computed(() => {
  return ['decimal', 'int', 'short', 'long', 'double', 'float'].includes(props.dataType);
});

const isDateTimeType = computed(() => {
  return props.dataType === 'DateTime';
});

const isDateType = computed(() => {
  return props.dataType === 'Date';
});

const isBoolType = computed(() => {
  return props.dataType === 'bool';
});

const isCharType = computed(() => {
  return props.dataType === 'char';
});

const isBinaryType = computed(() => {
  return props.dataType === 'byte[]';
});

const isObjectOrArrayType = computed(() => {
  return ['object', 'array'].includes(props.dataType);
});

// 数值类型配置
const getDecimalPlaces = () => {
  switch (props.dataType) {
    case 'decimal':
    case 'double':
    case 'float':
      return 2;
    default:
      return 0;
  }
};

const getMinValue = () => {
  switch (props.dataType) {
    case 'short':
      return -32768;
    case 'int':
      return -2147483648;
    case 'long':
      return Number.MIN_SAFE_INTEGER;
    default:
      return undefined;
  }
};

const getMaxValue = () => {
  switch (props.dataType) {
    case 'short':
      return 32767;
    case 'int':
      return 2147483647;
    case 'long':
      return Number.MAX_SAFE_INTEGER;
    default:
      return undefined;
  }
};

// JSON 相关
const getJsonPlaceholder = () => {
  if (props.dataType === 'object') {
    return '请输入JSON对象，例如：\n{\n  "key": "value",\n  "number": 123\n}';
  } else if (props.dataType === 'array') {
    return '请输入JSON数组，例如：\n[\n  "item1",\n  "item2",\n  123\n]';
  }
  return '请输入JSON格式数据';
};

const validateJson = () => {
  if (!jsonString.value.trim()) {
    jsonError.value = '';
    return;
  }

  try {
    const parsed = JSON.parse(jsonString.value);

    if (props.dataType === 'object' && (typeof parsed !== 'object' || Array.isArray(parsed))) {
      jsonError.value = '请输入有效的JSON对象';
      return;
    }

    if (props.dataType === 'array' && !Array.isArray(parsed)) {
      jsonError.value = '请输入有效的JSON数组';
      return;
    }

    jsonError.value = '';
    objectValue.value = parsed;
    onInputChange();
  } catch (error) {
    jsonError.value = 'JSON格式错误，请检查语法';
  }
};

const onJsonChange = () => {
  // 延迟验证，避免输入过程中频繁报错
  setTimeout(validateJson, 500);
};

// 文件上传相关
const uploadAction = '/api/upload'; // 根据实际情况修改
const beforeUpload = (file: File) => {
  // 可以在这里添加文件类型和大小验证
  return true;
};

const onUploadSuccess = (response: any) => {
  // 处理上传成功
  stringValue.value = response.data; // 假设返回文件路径或base64
  onInputChange();
  MessagePlugin.success('文件上传成功');
};

const onUploadError = (error: any) => {
  MessagePlugin.error('文件上传失败');
  console.error('Upload error:', error);
};

// 输入变化处理
const onInputChange = () => {
  let currentValue: any;

  switch (props.dataType) {
    case 'decimal':
    case 'double':
    case 'float':
    case 'int':
    case 'short':
    case 'long':
      currentValue = numberValue.value;
      break;
    case 'bool':
      currentValue = boolValue.value;
      break;
    case 'Date':
    case 'DateTime':
      currentValue = dateValue.value;
      break;
    case 'object':
    case 'array':
      currentValue = objectValue.value;
      break;
    default:
      currentValue = stringValue.value;
      break;
  }

  const stringResult = convertTypeToString(currentValue, props.dataType);
  emits('update:modelValue', stringResult);
  emits('change', stringResult);
};

// 初始化内部值的函数
const initializeValues = (value: string, dataType: string) => {
  const stringVal = value || '';

  try {
    switch (dataType) {
      case 'decimal':
      case 'double':
      case 'float':
      case 'int':
      case 'short':
      case 'long':
        numberValue.value = stringVal ? parseFloat(stringVal) : 0;
        break;
      case 'bool':
        boolValue.value = stringVal === 'true' || stringVal === '1';
        break;
      case 'Date':
      case 'DateTime':
        dateValue.value = stringVal;
        break;
      case 'object':
      case 'array':
        if (stringVal) {
          objectValue.value = JSON.parse(stringVal);
          jsonString.value = JSON.stringify(objectValue.value, null, 2);
        } else {
          objectValue.value = dataType === 'array' ? [] : {};
          jsonString.value = JSON.stringify(objectValue.value, null, 2);
        }
        break;
      default:
        stringValue.value = stringVal;
        break;
    }
  } catch (error) {
    // 转换失败时使用默认值
    switch (dataType) {
      case 'decimal':
      case 'double':
      case 'float':
      case 'int':
      case 'short':
      case 'long':
        numberValue.value = 0;
        break;
      case 'bool':
        boolValue.value = false;
        break;
      case 'object':
        objectValue.value = {};
        jsonString.value = '{}';
        break;
      case 'array':
        objectValue.value = [];
        jsonString.value = '[]';
        break;
      default:
        stringValue.value = stringVal;
        break;
    }
  }
};

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    initializeValues(newValue || '', props.dataType);
  },
  { immediate: true },
);

// 监听数据类型变化，重新转换值
watch(
  () => props.dataType,
  (newDataType) => {
    initializeValues(props.modelValue || '', newDataType);
  },
);
</script>

<style lang="less" scoped>
.data-type-input {
  width: 100%;

  .json-editor-container {
    .json-error {
      margin-top: 8px;
    }
  }

  /* 结构化编辑器样式已移至 StructuredEditor.vue 组件 */
}
</style>
