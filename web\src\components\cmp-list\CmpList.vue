<template>
  <div class="container">
    <t-row class="body">
      <t-col flex="1" class="content left">
        <div class="header-container">
          <p class="header">
            {{ props.name }}
          </p>
          <slot name="header"></slot>
        </div>
        <t-input v-model="searchKeyword" placeholder="搜索" clearable style="margin-bottom: 8px">
          <template #suffixIcon>
            <search-icon />
          </template>
        </t-input>
        <t-list v-if="isSearch" class="full-list">
          <t-list-item
            v-for="item in listAfterSearch"
            :key="item?.value"
            class="select-item"
            @click="onClickItem(item)"
          >
            <slot name="item" :item="item"></slot>
            <cmp-list-item-meta
              v-if="!$slots.item"
              :avatar-label="item.cmp?.avatarLabel"
              :name="item.cmp?.name || item.label"
              :sub-name="item.cmp?.subName"
              :code="item.cmp?.code"
              :description="item.cmp?.description || item.value"
              :show-icon="item.cmp?.showIcon"
              :suffix-tag="item.cmp?.suffixTag"
              :show-success="selectIds.indexOf(item.value) >= 0"
            ></cmp-list-item-meta>
          </t-list-item>
        </t-list>
        <t-list
          v-else-if="props.type === 'list'"
          class="full-list"
          :async-loading="asyncLoading"
          @load-more="onLoadMore"
          @scroll="onScrollList"
        >
          <t-list-item v-for="item in listData" :key="item?.value" class="select-item" @click="onClickItem(item)">
            <slot name="item" :item="item"></slot>
            <cmp-list-item-meta
              v-if="!$slots.item"
              :avatar-label="item.cmp?.avatarLabel"
              :name="item.cmp?.name || item.label"
              :sub-name="item.cmp?.subName"
              :code="item.cmp?.code"
              :description="item.cmp?.description || item.value"
              :show-icon="item.cmp?.showIcon"
              :suffix-tag="item.cmp?.suffixTag"
              :show-success="selectIds.indexOf(item.value) >= 0"
            ></cmp-list-item-meta>
          </t-list-item>
        </t-list>
        <div v-else-if="props.type === 'tree'" class="full-list">
          <!-- expand-on-click-node -->
          <t-tree :data="listData" hover :load="onLoadTreeNodes" activable value-mode="all" @expand="onExpandTree">
            <template #label="{ node }">
              <div @click="onClickItem(node.data)">
                <slot name="item" :item="node.data"></slot>
                <div v-if="!$slots.item">
                  <template v-if="node.data?.row?.children">
                    <span>{{ node.label }}</span>
                    <slot name="tree-actions" :item="node.data"></slot>
                  </template>
                  <cmp-list-item-meta
                    v-else
                    :avatar-label="node.data.cmp?.avatarLabel"
                    :name="node.data.cmp?.name || node.data.label"
                    :sub-name="node.data.cmp?.subName"
                    :code="node.data.cmp?.code"
                    :description="node.data.cmp?.description || node.data.value"
                    :show-icon="node.data.cmp?.showIcon"
                    :suffix-tag="node.data.cmp?.suffixTag"
                    :show-success="selectIds.indexOf(node.data.value) >= 0"
                  ></cmp-list-item-meta>
                </div>
              </div>
            </template>
          </t-tree>
        </div>
      </t-col>
    </t-row>
    <div class="footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>
<script lang="tsx">
export default {
  name: 'CmpList',
};
</script>
<script setup lang="tsx">
import { debounce, isEmpty } from 'lodash';
import { ListProps, TreeNodeModel, TreeOptionData, TreeProps } from 'tdesign-vue-next';
import { computed, ref, watch } from 'vue';

import CmpListItemMeta from './CmpListItemMeta.vue';
import { BusinessItem } from './constants';

export interface CmpListProps {
  type?: 'list' | 'tree';
  name: string;
  listProps?: ListProps;
  treeProps?: TreeProps;
  fetchData: (pageIndex?: number) => Promise<BusinessItem[]>;
  fetchSearchData: (keyword: string, listData: any[]) => Promise<BusinessItem[]>;
  fetchTreeNodeData?: (
    key: string | number,
    children: any[],
    node?: TreeNodeModel<TreeOptionData>,
  ) => Promise<BusinessItem[]>;
}
const props = withDefaults(defineProps<CmpListProps>(), {
  type: 'list',
});

const asyncLoading = ref<ListProps['asyncLoading']>('load-more');
const onLoadMore: ListProps['onLoadMore'] = () => {
  if (asyncLoading.value === '') return;
  asyncLoading.value = 'loading';
  pageIndex.value++;
  fetchListData();
};
const onScrollList: ListProps['onScroll'] = debounce((e) => {
  if (e.scrollBottom <= 1) {
    onLoadMore(null);
  }
}, 200);

const listData = ref<BusinessItem[]>([]);
const pageIndex = ref(1);
const fetchListData = async () => {
  const result = await props.fetchData(pageIndex.value);
  if (pageIndex.value === 1) {
    listData.value = [];
  }
  if (result.length === 0) {
    asyncLoading.value = '';
  } else {
    asyncLoading.value = pageIndex.value === 1 && result.length < 10 ? '' : 'load-more';
    listData.value = [...listData.value, ...result] as BusinessItem[];
  }
};

const emit = defineEmits(['click']);
const selectIds = computed<string[]>(() => {
  return [(selectItems.value as BusinessItem)?.value];
});
const selectItems = ref<BusinessItem | BusinessItem[]>(undefined);
const onClickItem = (item: BusinessItem) => {
  selectItems.value = item;
  emit('click', selectItems.value);
};
const onExpandTree = (val, context) => {
  if (context.node.expanded && context.trigger === 'icon-click') {
    onClickItem(context.node.data);
  }
};

const listAfterSearch = ref<BusinessItem[]>(undefined);
const isSearch = ref(false);
const searchKeyword = ref('');

watch(
  searchKeyword,
  debounce(async (val) => {
    if (isEmpty(val)) {
      listAfterSearch.value = undefined;
      isSearch.value = false;
      return;
    }
    isSearch.value = true;
    listAfterSearch.value = await props.fetchSearchData(val, listData.value);
  }, 500),
);

const onLoadTreeNodes: TreeProps['load'] = (node) => {
  return props?.fetchTreeNodeData(node.data.row?.id as string, node.data.row?.children as any[], node);
};

defineExpose({
  loadData: () => {
    pageIndex.value = 1;
    fetchListData();
  },
});
</script>
<style scoped lang="less">
.container {
  display: flex;
  flex-direction: column;
  flex: 1;

  > .filter-conditions {
    padding: 10px 15px;
  }

  > .body {
    flex-flow: row nowrap;
    padding: 0 16px;
    flex: 1;
    flex-direction: column;

    &:has(.t-table) {
      padding: 1px;
    }

    :deep(.t-table) {
      font-size: 12px;
      border: 0;
    }

    > .content {
      height: 330px;
      display: flex;
      flex-direction: column;

      &.right {
        margin: 10px 0;
        padding-left: 10px !important;
        border-left: 1px solid var(--td-border-level-1-color);
      }

      &.left {
        margin-top: 10px;
        width: 100%;
      }

      > .header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        > .header {
          margin-top: 0;
          font-size: 14px;
          font-weight: 600;
          color: #36434d;
          line-height: 20px;
          padding: 5px 0;

          .action-btn {
            float: right;
          }
        }
      }

      > .full-list {
        flex: 1;
        overflow-y: auto;

        :deep(.t-tree__label:has(.b-item)) {
          padding: 0;
        }

        :deep(.t-tree__icon:empty) {
          width: 0;
        }

        :deep(.t-is-active .t-tree__label) {
          background-color: var(--td-bg-color-container-active);
        }
      }
    }
  }

  > .footer {
    padding: 10px 15px;

    &::after {
      content: '';
      display: table;
      clear: both;
    }
  }
}

.select-item {
  padding: 0;

  &:hover {
    background-color: var(--td-bg-color-container-hover);
    border-radius: var(--td-radius-default);
  }
}
</style>
