namespace GCP.Models
{
    /// <summary>
    /// 通知通道类型
    /// </summary>
    public enum NotificationChannelType
    {
        Email,
        Dingtalk,
        Feishu,
        Weixin
    }

    /// <summary>
    /// 通知通道配置基类
    /// </summary>
    public abstract class NotificationChannelConfig
    {
        /// <summary>
        /// 通道类型
        /// </summary>
        public abstract NotificationChannelType ChannelType { get; }
    }

    /// <summary>
    /// 邮件通知配置
    /// </summary>
    public class EmailNotificationConfig : NotificationChannelConfig
    {
        public override NotificationChannelType ChannelType => NotificationChannelType.Email;
        
        /// <summary>
        /// SMTP服务器地址
        /// </summary>
        public string Host { get; set; } = null!;
        
        /// <summary>
        /// SMTP端口
        /// </summary>
        public int Port { get; set; }
        
        /// <summary>
        /// 发送人名称
        /// </summary>
        public string FromName { get; set; } = null!;
        
        /// <summary>
        /// 发送人邮箱
        /// </summary>
        public string FromAddress { get; set; } = null!;
        
        /// <summary>
        /// 邮箱密码或授权码
        /// </summary>
        public string Password { get; set; } = null!;
        
        /// <summary>
        /// 收件人列表
        /// </summary>
        public List<string> ToAddress { get; set; } = new();
        
        /// <summary>
        /// 是否启用SSL
        /// </summary>
        public bool EnableSsl { get; set; } = true;
    }

    /// <summary>
    /// 钉钉通知配置
    /// </summary>
    public class DingtalkNotificationConfig : NotificationChannelConfig
    {
        public override NotificationChannelType ChannelType => NotificationChannelType.Dingtalk;

        /// <summary>
        /// 通知类型：robot-群机器人，workNotice-工作通知
        /// </summary>
        public string NotificationType { get; set; } = "robot";

        // 群机器人配置
        /// <summary>
        /// Webhook地址
        /// </summary>
        public string WebHook { get; set; } = null!;

        /// <summary>
        /// 签名密钥
        /// </summary>
        public string Secret { get; set; }

        /// <summary>
        /// @所有人
        /// </summary>
        public bool IsAtAll { get; set; }

        /// <summary>
        /// @指定人员手机号列表
        /// </summary>
        public List<string> AtMobiles { get; set; } = new();

        // 工作通知配置
        /// <summary>
        /// 企业内部应用的AppKey
        /// </summary>
        public string AppKey { get; set; }

        /// <summary>
        /// 企业内部应用的AppSecret
        /// </summary>
        public string AppSecret { get; set; }

        /// <summary>
        /// 应用的AgentId
        /// </summary>
        public long AgentId { get; set; }

        /// <summary>
        /// 接收通知的用户ID列表
        /// </summary>
        public List<string> UserIds { get; set; } = new();

        /// <summary>
        /// 接收通知的部门ID列表
        /// </summary>
        public List<string> DeptIds { get; set; } = new();

        /// <summary>
        /// 是否发送给全员
        /// </summary>
        public bool ToAllUser { get; set; }
    }

    /// <summary>
    /// 飞书通知配置
    /// </summary>
    public class FeishuNotificationConfig : NotificationChannelConfig
    {
        public override NotificationChannelType ChannelType => NotificationChannelType.Feishu;
        
        /// <summary>
        /// Webhook地址
        /// </summary>
        public string WebHook { get; set; } = null!;
        
        /// <summary>
        /// 签名密钥
        /// </summary>
        public string Secret { get; set; }
    }

    /// <summary>
    /// 企业微信通知配置
    /// </summary>
    public class WeixinNotificationConfig : NotificationChannelConfig
    {
        public override NotificationChannelType ChannelType => NotificationChannelType.Weixin;
        
        /// <summary>
        /// Webhook地址
        /// </summary>
        public string WebHook { get; set; } = null!;
        
        /// <summary>
        /// 提及用户列表
        /// </summary>
        public List<string> MentionedList { get; set; } = new();
        
        /// <summary>
        /// 提及手机号列表
        /// </summary>
        public List<string> MentionedMobileList { get; set; } = new();
    }

    /// <summary>
    /// 通知请求
    /// </summary>
    public class NotificationRequest
    {
        /// <summary>
        /// 通知标题
        /// </summary>
        public string Title { get; set; } = null!;

        /// <summary>
        /// 通知内容
        /// </summary>
        public string Content { get; set; } = null!;

        /// <summary>
        /// 异常信息（可选）
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 动态用户配置（用于覆盖通道默认配置）
        /// </summary>
        public DynamicNotificationConfig DynamicConfig { get; set; }
    }

    /// <summary>
    /// 动态通知配置（用于在发送时临时覆盖通道配置）
    /// </summary>
    public class DynamicNotificationConfig
    {
        /// <summary>
        /// 钉钉动态配置
        /// </summary>
        public DingtalkDynamicConfig Dingtalk { get; set; }

        /// <summary>
        /// 邮件动态配置
        /// </summary>
        public EmailDynamicConfig Email { get; set; }

        /// <summary>
        /// 企业微信动态配置
        /// </summary>
        public WeixinDynamicConfig Weixin { get; set; }

        /// <summary>
        /// 飞书动态配置
        /// </summary>
        public FeishuDynamicConfig Feishu { get; set; }
    }

    /// <summary>
    /// 钉钉动态配置
    /// </summary>
    public class DingtalkDynamicConfig
    {
        /// <summary>
        /// 额外的接收用户ID列表（会与通道配置合并）
        /// </summary>
        public List<string> AdditionalUserIds { get; set; } = new();

        /// <summary>
        /// 额外的接收部门ID列表（会与通道配置合并）
        /// </summary>
        public List<string> AdditionalDeptIds { get; set; } = new();

        /// <summary>
        /// 额外的@手机号列表（会与通道配置合并）
        /// </summary>
        public List<string> AdditionalAtMobiles { get; set; } = new();

        /// <summary>
        /// 是否覆盖通道的@所有人设置
        /// </summary>
        public bool? OverrideIsAtAll { get; set; }

        /// <summary>
        /// 是否覆盖通道的发送给全员设置
        /// </summary>
        public bool? OverrideToAllUser { get; set; }
    }

    /// <summary>
    /// 邮件动态配置
    /// </summary>
    public class EmailDynamicConfig
    {
        /// <summary>
        /// 额外的收件人邮箱列表（会与通道配置合并）
        /// </summary>
        public List<string> AdditionalToAddresses { get; set; } = new();

        /// <summary>
        /// 额外的抄送邮箱列表（会与通道配置合并）
        /// </summary>
        public List<string> AdditionalCcAddresses { get; set; } = new();
    }

    /// <summary>
    /// 企业微信动态配置
    /// </summary>
    public class WeixinDynamicConfig
    {
        /// <summary>
        /// 额外的提及用户列表（会与通道配置合并）
        /// </summary>
        public List<string> AdditionalMentionedList { get; set; } = new();

        /// <summary>
        /// 额外的提及手机号列表（会与通道配置合并）
        /// </summary>
        public List<string> AdditionalMentionedMobileList { get; set; } = new();
    }

    /// <summary>
    /// 飞书动态配置
    /// </summary>
    public class FeishuDynamicConfig
    {
        /// <summary>
        /// 额外的@用户列表（会与通道配置合并）
        /// </summary>
        public List<string> AdditionalAtUsers { get; set; } = new();

        /// <summary>
        /// 额外的@手机号列表（会与通道配置合并）
        /// </summary>
        public List<string> AdditionalAtMobiles { get; set; } = new();
    }
}
