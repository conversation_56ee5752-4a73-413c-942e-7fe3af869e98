using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.Services;
using System.Text.Json;
using LinqToDB;

namespace GCP.FunctionPool.ActionLibrary
{
    /// <summary>
    /// 动作库执行器
    /// </summary>
    public class ActionLibraryExecutor
    {
        private readonly FunctionContext _context;
        private readonly string _solutionId;
        private readonly string _projectId;

        public ActionLibraryExecutor(FunctionContext context, string solutionId, string projectId)
        {
            _context = context;
            _solutionId = solutionId;
            _projectId = projectId;
        }

        /// <summary>
        /// 执行动作库
        /// </summary>
        /// <param name="actionLibraryId">动作库ID</param>
        /// <param name="inputData">输入数据</param>
        /// <param name="flowProcId">流程实例ID（可选）</param>
        /// <param name="flowStepId">流程步骤ID（可选）</param>
        /// <returns>执行结果</returns>
        public async Task<ActionLibraryExecutionResult> ExecuteAsync(string actionLibraryId, 
            object inputData = null, string flowProcId = null, string flowStepId = null)
        {
            var executionId = TUID.NewTUID().ToString();
            var startTime = DateTime.Now;
            
            try
            {
                // 获取动作库信息
                var actionLibraryService = new ActionLibraryService();
                var actionLibrary = actionLibraryService.Get(actionLibraryId);
                if (actionLibrary == null)
                    throw new CustomException($"动作库不存在: {actionLibraryId}");

                if (actionLibrary.Status != "active")
                    throw new CustomException($"动作库未激活: {actionLibrary.Name}");

                // 获取函数流
                var functionService = new FunctionService();
                var function = functionService.Get(actionLibrary.FunctionFlowId);
                if (function == null)
                    throw new CustomException($"关联的函数流不存在: {actionLibrary.FunctionFlowId}");

                // 获取函数流代码
                var functionCodeService = new FunctionCodeService();
                var version = actionLibrary.FunctionFlowVersion ?? function.UseVersion;
                var code = functionCodeService.GetCodeByVersion(actionLibrary.FunctionFlowId, version);
                if (string.IsNullOrEmpty(code))
                    throw new CustomException($"函数流代码不存在: {actionLibrary.FunctionFlowId}, version: {version}");

                var flowInfo = JsonSerializer.Deserialize<FunctionFlow>(code);
                if (flowInfo == null)
                    throw new CustomException("函数流代码格式错误");

                // 创建子执行上下文
                var subContext = CreateSubContext(_context, executionId, inputData);

                // 执行函数流
                var flowRunner = new FlowExecutor();
                flowRunner.Load(flowInfo);
                var result = await flowRunner.Run(subContext);

                var endTime = DateTime.Now;
                var executionTime = (int)(endTime - startTime).TotalMilliseconds;

                // 记录成功日志
                await LogExecutionAsync(actionLibraryService, actionLibraryId, executionId, inputData, result, 
                    "success", executionTime, startTime, endTime, null, null, flowProcId, flowStepId);

                // 更新统计信息
                await UpdateExecutionStatsAsync(actionLibraryService, actionLibraryId, executionTime);

                return new ActionLibraryExecutionResult
                {
                    Success = true,
                    Result = result,
                    ExecutionTime = executionTime,
                    ExecutionId = executionId,
                    ActionLibraryId = actionLibraryId,
                    ActionLibraryName = actionLibrary.Name
                };
            }
            catch (Exception ex)
            {
                var endTime = DateTime.Now;
                var executionTime = (int)(endTime - startTime).TotalMilliseconds;

                // 记录错误日志
                var actionLibraryService = new ActionLibraryService();
                await LogExecutionAsync(actionLibraryService, actionLibraryId, executionId, inputData, null, 
                    "error", executionTime, startTime, endTime, ex.Message, ex.StackTrace, flowProcId, flowStepId);

                return new ActionLibraryExecutionResult
                {
                    Success = false,
                    Error = ex.Message,
                    ExecutionTime = executionTime,
                    ExecutionId = executionId,
                    ActionLibraryId = actionLibraryId
                };
            }
        }

        /// <summary>
        /// 批量执行动作库
        /// </summary>
        /// <param name="executions">执行配置列表</param>
        /// <returns>执行结果列表</returns>
        public async Task<List<ActionLibraryExecutionResult>> ExecuteBatchAsync(
            List<ActionLibraryExecutionConfig> executions)
        {
            var results = new List<ActionLibraryExecutionResult>();
            
            foreach (var execution in executions)
            {
                var result = await ExecuteAsync(execution.ActionLibraryId, execution.InputData, 
                    execution.FlowProcId, execution.FlowStepId);
                results.Add(result);
                
                // 如果配置了失败时停止，且当前执行失败，则停止后续执行
                if (!result.Success && execution.StopOnError)
                    break;
            }
            
            return results;
        }

        /// <summary>
        /// 创建子执行上下文
        /// </summary>
        private FunctionContext CreateSubContext(FunctionContext parentContext, string executionId, object inputData)
        {
            var subContext = new FunctionContext
            {
                clientID = parentContext.clientID,
                trackId = executionId,
                globalData = new Dictionary<string, object>(),
                Persistence = parentContext.Persistence
            };
            subContext.SolutionId = parentContext.SolutionId;
            subContext.ProjectId = parentContext.ProjectId;
            subContext.LocalDbContext.Value = parentContext.LocalDbContext.Value;

            // 设置输入数据
            if (inputData != null)
            {
                if (inputData is string jsonStr)
                {
                    try
                    {
                        var parsedData = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonStr);
                        foreach (var kvp in parsedData)
                        {
                            subContext.globalData[kvp.Key] = kvp.Value;
                        }
                    }
                    catch
                    {
                        subContext.globalData["input"] = jsonStr;
                    }
                }
                else if (inputData is IDictionary<string, object> dictData)
                {
                    foreach (var kvp in dictData)
                    {
                        subContext.globalData[kvp.Key] = kvp.Value;
                    }
                }
                else
                {
                    subContext.globalData["input"] = inputData;
                }
            }

            return subContext;
        }

        /// <summary>
        /// 记录执行日志
        /// </summary>
        private async Task LogExecutionAsync(ActionLibraryService actionLibraryService, string actionLibraryId, 
            string executionId, object inputData, object outputData, string status, int executionTime, 
            DateTime startTime, DateTime endTime, string errorMessage = null, string stackTrace = null, 
            string flowProcId = null, string flowStepId = null)
        {
            try
            {
                await using var db = new GcpDb();
                
                var log = new LcActionLibraryLog
                {
                    Id = TUID.NewTUID().ToString(),
                    ActionLibraryId = actionLibraryId,
                    ExecutionId = executionId,
                    FlowProcId = flowProcId,
                    FlowStepId = flowStepId,
                    InputData = inputData != null ? JsonSerializer.Serialize(inputData) : null,
                    OutputData = outputData != null ? JsonSerializer.Serialize(outputData) : null,
                    ErrorMessage = errorMessage,
                    StackTrace = stackTrace,
                    Status = status,
                    ExecutionTimeMs = executionTime,
                    StartTime = startTime,
                    EndTime = endTime,
                    SolutionId = _solutionId,
                    ProjectId = _projectId,
                    Creator = _context.clientID
                };

                await db.InsertAsync(log);
            }
            catch (Exception ex)
            {
                // 记录日志失败不应该影响主流程
                Console.WriteLine($"记录动作库执行日志失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新执行统计信息
        /// </summary>
        private async Task UpdateExecutionStatsAsync(ActionLibraryService actionLibraryService, 
            string actionLibraryId, int executionTime)
        {
            try
            {
                await using var db = new GcpDb();
                
                var actionLibrary = db.LcActionLibraries
                    .FirstOrDefault(a => a.Id == actionLibraryId);

                if (actionLibrary != null)
                {
                    actionLibrary.ExecutionCount++;
                    actionLibrary.LastExecutionTime = DateTime.Now;
                    
                    // 计算平均执行时间
                    if (actionLibrary.AverageExecutionTime.HasValue)
                    {
                        actionLibrary.AverageExecutionTime = 
                            (int)((actionLibrary.AverageExecutionTime.Value * (actionLibrary.ExecutionCount - 1) + executionTime) 
                                  / actionLibrary.ExecutionCount);
                    }
                    else
                    {
                        actionLibrary.AverageExecutionTime = executionTime;
                    }

                    await db.UpdateAsync(actionLibrary);
                }
            }
            catch (Exception ex)
            {
                // 更新统计信息失败不应该影响主流程
                Console.WriteLine($"更新动作库统计信息失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 动作库执行结果
    /// </summary>
    public class ActionLibraryExecutionResult
    {
        public bool Success { get; set; }
        public object Result { get; set; }
        public string Error { get; set; }
        public int ExecutionTime { get; set; }
        public string ExecutionId { get; set; }
        public string ActionLibraryId { get; set; }
        public string ActionLibraryName { get; set; }
    }

    /// <summary>
    /// 动作库执行配置
    /// </summary>
    public class ActionLibraryExecutionConfig
    {
        public string ActionLibraryId { get; set; }
        public object InputData { get; set; }
        public string FlowProcId { get; set; }
        public string FlowStepId { get; set; }
        public bool StopOnError { get; set; } = false;
    }
}
