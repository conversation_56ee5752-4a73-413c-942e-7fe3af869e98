using GCP.Common;
using GCP.FunctionPool.Flow.Models;
using System.Diagnostics;
using System.Text;
using TouchSocket.Core;
using TouchSocket.Sockets;

namespace GCP.FunctionPool.Flow.Services
{
    /// <summary>
    /// TCP请求服务
    /// </summary>
    class TcpRequest : DataBaseService
    {
        [Function("tcpRequest", "TCP 请求")]
        public async Task<object> RequestTcp(DataTcpRequest dataRequest)
        {
            var engine = this.GetEngine();
            var startTime = DateTime.Now;
            var connectionTime = 0L;
            var responseTime = 0L;
            
            try
            {
                // 获取参数值
                var host = GetDataValue(dataRequest.Host, engine)?.ToString();
                var port = Convert.ToInt32(GetDataValue(dataRequest.Port, engine));
                var message = GetDataValue(dataRequest.Message, engine)?.ToString();
                var timeout = Convert.ToInt32(GetDataValue(dataRequest.Timeout, engine) ?? 5000);
                var responseTimeout = Convert.ToInt32(GetDataValue(dataRequest.ResponseTimeout, engine) ?? 10000);

                if (string.IsNullOrEmpty(host))
                {
                    throw new CustomException("主机地址不能为空");
                }

                if (port <= 0 || port > 65535)
                {
                    throw new CustomException("端口号必须在1-65535之间");
                }

                if (string.IsNullOrEmpty(message))
                {
                    throw new CustomException("消息内容不能为空");
                }

                // 记录日志
                if (this.Context.Persistence)
                {
                    this.Context.Current.ArgsBuilder = new StringBuilder();
                    this.Context.Current.ArgsBuilder.AppendLine($"TCP连接: {host}:{port}");
                    this.Context.Current.ArgsBuilder.AppendLine($"消息格式: {dataRequest.MessageFormat}");
                    this.Context.Current.ArgsBuilder.AppendLine($"消息内容: {message}");
                    this.Context.Current.ArgsBuilder.AppendLine($"等待响应: {dataRequest.WaitForResponse}");
                }

                // 创建TCP客户端
                var client = new TcpClient();
                var connectionStopwatch = Stopwatch.StartNew();

                try
                {
                    // 设置连接配置
                    var config = new TouchSocketConfig()
                        .SetRemoteIPHost($"{host}:{port}");
                    config = dataRequest.MessageFormat == "json" ? config.SetTcpDataHandlingAdapter(() => new JsonPackageAdapter(dataRequest.Encoding == "ascii" ? Encoding.ASCII : Encoding.UTF8)) : config.SetTcpDataHandlingAdapter(() => new NormalDataHandlingAdapter());

                    await client.SetupAsync(config);

                    // 连接到服务器
                    await client.ConnectAsync();
                    connectionStopwatch.Stop();
                    connectionTime = connectionStopwatch.ElapsedMilliseconds;

                    // 准备发送的消息
                    byte[] messageBytes = PrepareMessage(message, dataRequest.MessageFormat, dataRequest.Encoding);

                    string response = null;
                    var responseStopwatch = Stopwatch.StartNew();

                    if (dataRequest.WaitForResponse)
                    {
                        // 设置接收事件处理
                        var responseReceived = false;
                        var responseData = new StringBuilder();
                        var responseEvent = new TaskCompletionSource<string>();

                        client.Received += (client, e) =>
                        {
                            try
                            {
                                var receivedData = ParseResponse(e.ByteBlock.ToArray(), dataRequest.Encoding);
                                responseData.Append(receivedData);
                                
                                if (!responseReceived)
                                {
                                    responseReceived = true;
                                    responseEvent.SetResult(responseData.ToString());
                                }
                            }
                            catch (Exception ex)
                            {
                                responseEvent.SetException(ex);
                            }
                            return Task.CompletedTask;
                        };

                        // 发送消息
                        await client.SendAsync(messageBytes);

                        // 等待响应
                        using var cts = new CancellationTokenSource(responseTimeout);
                        try
                        {
                            response = await responseEvent.Task.WaitAsync(cts.Token);
                        }
                        catch (OperationCanceledException)
                        {
                            throw new CustomException($"等待响应超时（{responseTimeout}ms）");
                        }
                    }
                    else
                    {
                        // 只发送消息，不等待响应
                        await client.SendAsync(messageBytes);
                        response = "消息已发送（未等待响应）";
                    }

                    responseStopwatch.Stop();
                    responseTime = responseStopwatch.ElapsedMilliseconds;

                    // 记录成功日志
                    if (this.Context.Persistence)
                    {
                        this.Context.Current.ArgsBuilder.AppendLine($"连接耗时: {connectionTime}ms");
                        this.Context.Current.ArgsBuilder.AppendLine($"响应耗时: {responseTime}ms");
                        this.Context.Current.ArgsBuilder.AppendLine($"响应内容: {response}");
                    }

                    return new
                    {
                        success = true,
                        response = response,
                        error = (string)null,
                        connectionTime = connectionTime,
                        responseTime = responseTime,
                        totalTime = (DateTime.Now - startTime).TotalMilliseconds
                    };
                }
                finally
                {
                    // 确保客户端被正确关闭
                    try
                    {
                        await client.CloseAsync();
                    }
                    catch
                    {
                        // 忽略关闭时的异常
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误日志
                if (this.Context.Persistence)
                {
                    this.Context.Current.ArgsBuilder?.AppendLine($"错误: {ex.Message}");
                }

                return new
                {
                    success = false,
                    response = (string)null,
                    error = ex.Message,
                    connectionTime = connectionTime,
                    responseTime = responseTime,
                    totalTime = (DateTime.Now - startTime).TotalMilliseconds
                };
            }
        }

        /// <summary>
        /// 准备发送的消息
        /// </summary>
        private byte[] PrepareMessage(string message, string messageFormat, string encoding)
        {
            try
            {
                switch (messageFormat?.ToLower())
                {
                    case "hex":
                        // 十六进制格式
                        return Convert.FromHexString(message.Replace(" ", "").Replace("-", ""));
                    
                    case "json":
                        // JSON格式，验证JSON有效性
                        System.Text.Json.JsonDocument.Parse(message);
                        return GetEncodingBytes(message, encoding);

                    default:
                        // 文本格式
                        return GetEncodingBytes(message, encoding);
                }
            }
            catch (Exception ex)
            {
                throw new CustomException($"消息格式化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析响应数据
        /// </summary>
        private string ParseResponse(byte[] data, string encoding)
        {
            try
            {
                return GetEncodingString(data, encoding);
            }
            catch (Exception ex)
            {
                throw new CustomException($"响应解析失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据编码格式获取字节数组
        /// </summary>
        private byte[] GetEncodingBytes(string text, string encoding)
        {
            return encoding?.ToLower() switch
            {
                "ascii" => Encoding.ASCII.GetBytes(text),
                "base64" => Convert.FromBase64String(text),
                "utf8" or _ => Encoding.UTF8.GetBytes(text)
            };
        }

        /// <summary>
        /// 根据编码格式获取字符串
        /// </summary>
        private string GetEncodingString(byte[] data, string encoding)
        {
            return encoding?.ToLower() switch
            {
                "ascii" => Encoding.ASCII.GetString(data),
                "base64" => Convert.ToBase64String(data),
                "utf8" or _ => Encoding.UTF8.GetString(data)
            };
        }
    }
}
