{"name": "gcp.web", "version": "1.2.0", "type": "module", "scripts": {"dev:mock": "vite --open --mode mock", "dev": "vite --open --mode development", "dev:linux": "vite --mode development", "build": "vite build --mode production", "build:dev": "vue-tsc --noEmit && vite build --mode development", "build:type": "vue-tsc --noEmit", "preview": "vite preview", "lint": "eslint --ext .vue,.js,.jsx,.ts,.tsx src/ --max-warnings 0", "lint:fix": "eslint --ext .vue,.js,jsx,.ts,.tsx src/ --max-warnings 0 --fix", "stylelint": "stylelint src/**/*.{html,vue,sass,less}", "stylelint:fix": "stylelint --fix src/**/*.{html,vue,css,sass,less}", "prepare": "husky install", "site:preview": "npm run build && cp -r dist _site", "test": "echo \"no test specified,work in process\"", "test:coverage": "echo \"no test:coverage specified,work in process\""}, "dependencies": {"@guolao/vue-monaco-editor": "^1.5.5", "@imengyu/vue3-context-menu": "^1.5.1", "@vue-js-cron/light": "^4.0.11", "@vueuse/core": "^13.5.0", "dayjs": "^1.11.13", "echarts": "5.6.0", "eventemitter3": "^5.0.1", "lodash-es": "^4.17.21", "monaco-editor": "^0.52.2", "nanoid": "^5.1.5", "nprogress": "^0.2.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "pinyin-match": "^1.2.8", "qrcode.vue": "^3.6.0", "spark-md5": "^3.0.2", "tdesign-icons-vue-next": "^0.3.6", "tdesign-vue-next": "^1.15.1", "tvision-color": "^1.6.0", "vue": "^3.5.18", "vue-draggable-plus": "^0.6.0", "vue-i18n": "^11.1.10", "vue-router": "^4.5.1"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@types/echarts": "^5.0.0", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@types/spark-md5": "^3.0.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/compiler-sfc": "~3.5.18", "@vue/eslint-config-typescript": "^13.0.0", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.31.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-vue": "^10.3.0", "eslint-plugin-vue-scoped-css": "^2.11.0", "husky": "^9.1.7", "less": "^4.4.0", "lint-staged": "^15.5.2", "mockjs": "^1.1.0", "postcss-html": "^1.8.0", "postcss-less": "^6.0.0", "prettier": "^3.6.2", "shiki": "^3.8.1", "stylelint": "~16.2.1", "stylelint-config-standard": "^36.0.1", "stylelint-order": "~6.0.4", "typescript": "~5.8.3", "vite": "^6.3.5", "vite-plugin-mock": "^3.0.2", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.2.12"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "engines": {"node": ">=18.0.0"}}