import { cloneDeep } from 'lodash-es';
import { defineStore } from 'pinia';

import { FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import { ROOT_NODE_KEY, createRootNode } from '@/components/action-panel/utils/rootNodeHelper';

import { ArgsInfo } from './model';

const initialState = (state) => {
  const actionFlowStore = useActionFlowStore();

  const currentArgs = actionFlowStore.currentStep.args || {};

  state.args = cloneDeep({
    ...({
      name: actionFlowStore.currentStep.name || '',
      description: '',
      host: {
        type: 'text',
        textValue: '127.0.0.1',
      },
      port: {
        type: 'text',
        dataType: 'number',
        textValue: '7789',
      },
      message: {
        type: 'text',
        textValue: '',
      },
      messageFormat: 'text',
      timeout: {
        type: 'text',
        dataType: 'number',
        textValue: '5000',
      },
      encoding: 'utf8',
      waitForResponse: true,
      responseTimeout: {
        type: 'text',
        dataType: 'number',
        textValue: '10000',
      },
    } as ArgsInfo),
    ...currentArgs,
  });

  // 初始化输出结果
  if (!actionFlowStore.currentStep.result || actionFlowStore.currentStep.result.length === 0) {
    const rootNode = createRootNode('TCP请求结果', 'object', {
      type: 'variable',
      variableType: 'current',
      variableValue: 'result',
    });

    // 为根节点添加默认的子节点
    rootNode.children = [
      {
        id: 'success',
        key: 'success',
        description: '请求是否成功',
        type: 'boolean',
        value: {
          type: 'variable',
          variableType: 'current',
          variableValue: 'result.success',
        },
        children: [],
      },
      {
        id: 'data',
        key: 'data',
        description: '服务器响应内容',
        type: 'string',
        value: {
          type: 'variable',
          variableType: 'current',
          variableValue: 'result.data',
        },
        children: [],
      },
      {
        id: 'message',
        key: 'message',
        description: '请求消息',
        type: 'string',
        value: {
          type: 'variable',
          variableType: 'current',
          variableValue: 'result.error',
        },
        children: [],
      },
    ];

    actionFlowStore.currentStep.result = [rootNode];
  }
};

export const useTcpRequestStore = defineStore('tcpRequest', {
  state: () => ({
    args: {} as ArgsInfo,
  }),
  actions: {
    updateState() {
      initialState(this);
    },
    setArgs(args: ArgsInfo) {
      const actionFlowStore = useActionFlowStore();
      actionFlowStore.currentStep.args = args;
    },
  },
});
