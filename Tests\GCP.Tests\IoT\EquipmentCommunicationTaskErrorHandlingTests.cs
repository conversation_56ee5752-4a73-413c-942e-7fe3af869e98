using Xunit;
using Xunit.Abstractions;
using Moq;
using GCP.Iot.Services;
using GCP.Iot.Models;
using GCP.Iot.Interfaces;
using EasyCaching.Core;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading;
using System;
using System.Linq;
using GCP.Tests.Infrastructure;

namespace GCP.Tests.IoT
{
    /// <summary>
    /// 测试设备通信任务的错误处理和降级逻辑
    /// </summary>
    public class EquipmentCommunicationTaskErrorHandlingTests : TestBase
    {
        private readonly Mock<IDriver> _mockDriver;
        private readonly Mock<IEasyCachingProvider> _mockCachingProvider;
        private readonly EquipmentEventManager _eventManager;
        private readonly EquipmentTypeConfig _typeConfig;

        public EquipmentCommunicationTaskErrorHandlingTests(ITestOutputHelper output) : base(output)
        {
            _mockDriver = new Mock<IDriver>();
            _mockCachingProvider = new Mock<IEasyCachingProvider>();
            _eventManager = new EquipmentEventManager();
            _typeConfig = new EquipmentTypeConfig();

            // 设置默认的驱动属性
            _mockDriver.Setup(d => d.IsConnected).Returns(true);
            _mockDriver.Setup(d => d.DriverCode).Returns("TestDriver");
            _mockDriver.Setup(d => d.MinSamplingPeriod).Returns(100);
        }

        [Fact]
        public async Task ReadBatchDataAsync_BatchReadException_ShouldFallbackToIndividualRead()
        {
            // Arrange
            var variables = new List<EquipmentVariable>
            {
                new EquipmentVariable { Id = "var1", VarName = "TestVar1", Address = "DB1", DataType = "Int32" },
                new EquipmentVariable { Id = "var2", VarName = "TestVar2", Address = "DB2", DataType = "Int32" }
            };

            _mockDriver.Setup(d => d.SupportsBatchReading).Returns(true);

            // 批量读取抛出异常
            _mockDriver.Setup(d => d.BatchReadAsync(It.IsAny<Dictionary<string, DataTypeEnum>>()))
                      .ThrowsAsync(new Exception("批量读取失败"));

            // 单个读取成功
            _mockDriver.Setup(d => d.ReadAsync("DB1", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Success, RawValue = 100 });
            _mockDriver.Setup(d => d.ReadAsync("DB2", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Success, RawValue = 200 });

            var task = new EquipmentCommunicationTask(
                "equipment1", "TestEquipment", "TestType",
                _mockDriver.Object, variables, _typeConfig,
                _eventManager, false, _mockCachingProvider.Object);

            // Act
            var results = await task.ReadBatchDataAsync(true, CancellationToken.None);

            // Assert
            Assert.Equal(2, results.Count);
            Assert.All(results, r => Assert.Equal(OperationStatus.Success, r.Result.Status));
            
            // 验证降级逻辑：先尝试批量读取，失败后使用单个读取
            _mockDriver.Verify(d => d.BatchReadAsync(It.IsAny<Dictionary<string, DataTypeEnum>>()), Times.Once);
            _mockDriver.Verify(d => d.ReadAsync("DB1", DataTypeEnum.Int32), Times.Once);
            _mockDriver.Verify(d => d.ReadAsync("DB2", DataTypeEnum.Int32), Times.Once);
        }

        [Fact]
        public async Task ReadBatchDataAsync_AllReadsFail_ShouldReturnErrorResults()
        {
            // Arrange
            var variables = new List<EquipmentVariable>
            {
                new EquipmentVariable { Id = "var1", VarName = "TestVar1", Address = "DB1", DataType = "Int32" },
                new EquipmentVariable { Id = "var2", VarName = "TestVar2", Address = "DB2", DataType = "Int32" }
            };

            _mockDriver.Setup(d => d.SupportsBatchReading).Returns(false);

            // 所有单个读取都失败
            _mockDriver.Setup(d => d.ReadAsync("DB1", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Failed, ErrorMessage = "读取DB1失败" });
            _mockDriver.Setup(d => d.ReadAsync("DB2", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Failed, ErrorMessage = "读取DB2失败" });

            var task = new EquipmentCommunicationTask(
                "equipment1", "TestEquipment", "TestType",
                _mockDriver.Object, variables, _typeConfig,
                _eventManager, false, _mockCachingProvider.Object);

            // Act
            var results = await task.ReadBatchDataAsync(false, CancellationToken.None);

            // Assert
            Assert.Equal(2, results.Count);
            Assert.All(results, r => Assert.Equal(OperationStatus.Failed, r.Result.Status));
            
            var result1 = results.First(r => r.Variable.VarName == "TestVar1");
            var result2 = results.First(r => r.Variable.VarName == "TestVar2");
            
            Assert.Equal("读取DB1失败", result1.Result.ErrorMessage);
            Assert.Equal("读取DB2失败", result2.Result.ErrorMessage);
        }

        [Fact]
        public async Task ReadBatchDataAsync_PartialFailure_ShouldReturnMixedResults()
        {
            // Arrange
            var variables = new List<EquipmentVariable>
            {
                new EquipmentVariable { Id = "var1", VarName = "TestVar1", Address = "DB1", DataType = "Int32" },
                new EquipmentVariable { Id = "var2", VarName = "TestVar2", Address = "DB2", DataType = "Int32" },
                new EquipmentVariable { Id = "var3", VarName = "TestVar3", Address = "DB3", DataType = "Int32" }
            };

            _mockDriver.Setup(d => d.SupportsBatchReading).Returns(false);

            // 部分成功，部分失败
            _mockDriver.Setup(d => d.ReadAsync("DB1", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Success, RawValue = 100 });
            _mockDriver.Setup(d => d.ReadAsync("DB2", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Failed, ErrorMessage = "读取DB2失败" });
            _mockDriver.Setup(d => d.ReadAsync("DB3", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Success, RawValue = 300 });

            var task = new EquipmentCommunicationTask(
                "equipment1", "TestEquipment", "TestType",
                _mockDriver.Object, variables, _typeConfig,
                _eventManager, false, _mockCachingProvider.Object);

            // Act
            var results = await task.ReadBatchDataAsync(false, CancellationToken.None);

            // Assert
            Assert.Equal(3, results.Count);

            var successResults = results.Where(r => r.Result.Status == OperationStatus.Success).ToList();
            var failedResults = results.Where(r => r.Result.Status == OperationStatus.Failed).ToList();

            Assert.Equal(2, successResults.Count);
            Assert.Single(failedResults);

            Assert.Contains(successResults, r => r.Variable.VarName == "TestVar1" && (int)r.Result.RawValue == 100);
            Assert.Contains(successResults, r => r.Variable.VarName == "TestVar3" && (int)r.Result.RawValue == 300);
            Assert.Contains(failedResults, r => r.Variable.VarName == "TestVar2" && r.Result.ErrorMessage == "读取DB2失败");
        }

        [Fact]
        public async Task ReadBatchDataAsync_CompleteException_ShouldReturnErrorForAllVariables()
        {
            // Arrange
            var variables = new List<EquipmentVariable>
            {
                new EquipmentVariable { Id = "var1", VarName = "TestVar1", Address = "DB1", DataType = "Int32" },
                new EquipmentVariable { Id = "var2", VarName = "TestVar2", Address = "DB2", DataType = "Int32" }
            };

            _mockDriver.Setup(d => d.SupportsBatchReading).Returns(false);

            // 所有读取都抛出异常
            _mockDriver.Setup(d => d.ReadAsync(It.IsAny<string>(), It.IsAny<DataTypeEnum>()))
                      .ThrowsAsync(new Exception("驱动完全失败"));

            var task = new EquipmentCommunicationTask(
                "equipment1", "TestEquipment", "TestType",
                _mockDriver.Object, variables, _typeConfig,
                _eventManager, false, _mockCachingProvider.Object);

            // Act
            var results = await task.ReadBatchDataAsync(false, CancellationToken.None);

            // Assert
            Assert.Equal(2, results.Count);
            Assert.All(results, r => Assert.Equal(OperationStatus.Failed, r.Result.Status));
            Assert.All(results, r => Assert.Contains("驱动完全失败", r.Result.ErrorMessage));
        }

        [Fact]
        public async Task ReadBatchDataAsync_WithAddressExpansion_BatchFailure_ShouldFallbackCorrectly()
        {
            // Arrange
            var variables = new List<EquipmentVariable>
            {
                new EquipmentVariable { Id = "var1", VarName = "TestVar1", Address = "DB1,DB2,DB3", DataType = "Int32" }
            };

            _mockDriver.Setup(d => d.SupportsBatchReading).Returns(true);

            // 批量读取失败
            _mockDriver.Setup(d => d.BatchReadAsync(It.IsAny<Dictionary<string, DataTypeEnum>>()))
                      .ThrowsAsync(new Exception("批量读取失败"));

            // 单个读取成功（用于降级）
            _mockDriver.Setup(d => d.ReadAsync("DB1", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Success, RawValue = 100 });
            _mockDriver.Setup(d => d.ReadAsync("DB2", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Success, RawValue = 200 });
            _mockDriver.Setup(d => d.ReadAsync("DB3", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Success, RawValue = 300 });

            var task = new EquipmentCommunicationTask(
                "equipment1", "TestEquipment", "TestType",
                _mockDriver.Object, variables, _typeConfig,
                _eventManager, false, _mockCachingProvider.Object);

            // Act
            var results = await task.ReadBatchDataAsync(true, CancellationToken.None);

            // Assert
            Assert.Single(results);
            var result = results.First();
            Assert.Equal(OperationStatus.Success, result.Result.Status);
            Assert.IsType<object[]>(result.Result.RawValue);
            
            var arrayResult = (object[])result.Result.RawValue;
            Assert.Equal(3, arrayResult.Length);
            Assert.Equal(100, arrayResult[0]);
            Assert.Equal(200, arrayResult[1]);
            Assert.Equal(300, arrayResult[2]);
        }

        [Fact]
        public async Task SmartBatchReading_OnlineToPartialFailure_ShouldIdentifyProblematicVariables()
        {
            // Arrange
            var variables = new List<EquipmentVariable>
            {
                new EquipmentVariable { Id = "var1", VarName = "TestVar1", Address = "DB1", DataType = "Int32" },
                new EquipmentVariable { Id = "var2", VarName = "TestVar2", Address = "DB2", DataType = "Int32" },
                new EquipmentVariable { Id = "var3", VarName = "TestVar3", Address = "DB3", DataType = "Int32" }
            };

            _mockDriver.Setup(d => d.SupportsBatchReading).Returns(true);

            // 第一次批量读取失败
            _mockDriver.Setup(d => d.BatchReadAsync(It.IsAny<Dictionary<string, DataTypeEnum>>()))
                      .ThrowsAsync(new Exception("批量读取失败"));

            // 单个读取：部分成功，部分失败
            _mockDriver.Setup(d => d.ReadAsync("DB1", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Success, RawValue = 100 });
            _mockDriver.Setup(d => d.ReadAsync("DB2", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Failed, ErrorMessage = "DB2读取失败" });
            _mockDriver.Setup(d => d.ReadAsync("DB3", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Success, RawValue = 300 });

            var task = new EquipmentCommunicationTask(
                "equipment1", "TestEquipment", "TestType",
                _mockDriver.Object, variables, _typeConfig,
                _eventManager, false, _mockCachingProvider.Object);

            // Act - 第一次读取，应该从在线状态变为部分失败状态
            var results1 = await task.ReadBatchDataAsync(true, CancellationToken.None);

            // Assert - 验证状态变更和结果
            Assert.Equal(3, results1.Count);
            Assert.Equal(GCP.Iot.Services.EquipmentCommunicationStatus.PartialFailure, task.CommunicationStatus);

            var successResults = results1.Where(r => r.Result.Status == OperationStatus.Success).ToList();
            var failedResults = results1.Where(r => r.Result.Status == OperationStatus.Failed).ToList();

            Assert.Equal(2, successResults.Count);
            Assert.Single(failedResults);
            Assert.Equal("TestVar2", failedResults.First().Variable.VarName);
        }

        [Fact]
        public async Task SmartBatchReading_PartialFailureState_ShouldSeparateProblematicVariables()
        {
            // Arrange
            var variables = new List<EquipmentVariable>
            {
                new EquipmentVariable { Id = "var1", VarName = "TestVar1", Address = "DB1", DataType = "Int32" },
                new EquipmentVariable { Id = "var2", VarName = "TestVar2", Address = "DB2", DataType = "Int32" },
                new EquipmentVariable { Id = "var3", VarName = "TestVar3", Address = "DB3", DataType = "Int32" }
            };

            _mockDriver.Setup(d => d.SupportsBatchReading).Returns(true);

            var task = new EquipmentCommunicationTask(
                "equipment1", "TestEquipment", "TestType",
                _mockDriver.Object, variables, _typeConfig,
                _eventManager, false, _mockCachingProvider.Object);

            // 先模拟第一次读取失败，进入部分失败状态
            _mockDriver.Setup(d => d.BatchReadAsync(It.IsAny<Dictionary<string, DataTypeEnum>>()))
                      .ThrowsAsync(new Exception("批量读取失败"));

            _mockDriver.Setup(d => d.ReadAsync("DB1", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Success, RawValue = 100 });
            _mockDriver.Setup(d => d.ReadAsync("DB2", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Failed, ErrorMessage = "DB2读取失败" });
            _mockDriver.Setup(d => d.ReadAsync("DB3", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Success, RawValue = 300 });

            await task.ReadBatchDataAsync(true, CancellationToken.None);

            // 现在重新设置 Mock，模拟第二次读取
            _mockDriver.Reset();
            _mockDriver.Setup(d => d.SupportsBatchReading).Returns(true);

            // 正常变量的批量读取成功
            _mockDriver.Setup(d => d.BatchReadAsync(It.Is<Dictionary<string, DataTypeEnum>>(d => d.Count == 2)))
                      .ReturnsAsync(new DriverOperationResult
                      {
                          Status = OperationStatus.Success,
                          RawValue = new Dictionary<string, object>
                          {
                              { "DB1", 101 },
                              { "DB3", 301 }
                          }
                      });

            // 异常变量的独立读取仍然失败
            _mockDriver.Setup(d => d.ReadAsync("DB2", DataTypeEnum.Int32))
                      .ReturnsAsync(new DriverOperationResult { Status = OperationStatus.Failed, ErrorMessage = "DB2仍然失败" });

            // Act - 第二次读取，应该分离处理
            var results2 = await task.ReadBatchDataAsync(true, CancellationToken.None);

            // Assert
            Assert.Equal(3, results2.Count);
            Assert.Equal(GCP.Iot.Services.EquipmentCommunicationStatus.PartialFailure, task.CommunicationStatus);

            var successResults = results2.Where(r => r.Result.Status == OperationStatus.Success).ToList();
            var failedResults = results2.Where(r => r.Result.Status == OperationStatus.Failed).ToList();

            Assert.Equal(2, successResults.Count);
            Assert.Single(failedResults);

            // 验证批量读取被调用（用于正常变量）
            _mockDriver.Verify(d => d.BatchReadAsync(It.Is<Dictionary<string, DataTypeEnum>>(dict => dict.Count == 2)), Times.Once);
            // 验证独立读取被调用（用于异常变量）
            _mockDriver.Verify(d => d.ReadAsync("DB2", DataTypeEnum.Int32), Times.Once);
        }

        [Fact]
        public void ResetCommunicationStatus_ShouldResetToOnlineState()
        {
            // Arrange
            var variables = new List<EquipmentVariable>
            {
                new EquipmentVariable { Id = "var1", VarName = "TestVar1", Address = "DB1", DataType = "Int32" }
            };

            _mockDriver.Setup(d => d.SupportsBatchReading).Returns(true);

            var task = new EquipmentCommunicationTask(
                "equipment1", "TestEquipment", "TestType",
                _mockDriver.Object, variables, _typeConfig,
                _eventManager, false, _mockCachingProvider.Object);

            // Act - 重置通信状态
            task.ResetCommunicationStatus();

            // Assert - 验证状态重置
            Assert.Equal(GCP.Iot.Services.EquipmentCommunicationStatus.Online, task.CommunicationStatus);
            Assert.True(task.IsOnline);

            var statusInfo = task.GetCommunicationStatusInfo();
            Assert.NotNull(statusInfo);

            // 通过反射检查状态信息
            var statusProperty = statusInfo.GetType().GetProperty("Status")?.GetValue(statusInfo);
            var problematicCountProperty = statusInfo.GetType().GetProperty("ProblematicVariablesCount")?.GetValue(statusInfo);

            Assert.Equal("Online", statusProperty);
            Assert.Equal(0, problematicCountProperty);
        }
    }
}
