﻿using EasyNotice.Core;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Text;
using System.Text.Json;

namespace EasyNotice.Dingtalk
{
    /// <summary>
    /// 配置钉钉群机器人官方文档
    /// https://developers.dingtalk.com/document/app/custom-robot-access
    /// </summary>
    internal class DingtalkProvider : IDingtalkProvider
    {
        private static HttpClient _httpClient = new HttpClient(new HttpClientHandler
        {
            AutomaticDecompression = System.Net.DecompressionMethods.GZip,
        });

        private readonly DingtalkOptions _dingTalkOptions;
        private readonly NoticeOptions _noticeOptions;

        public DingtalkProvider(IOptions<DingtalkOptions> dingTalkOptions, IOptions<NoticeOptions> noticeOptions)
        {
            _dingTalkOptions = dingTalkOptions.Value;
            _noticeOptions = noticeOptions.Value;
        }

        /// <summary>
        /// 发送异常消息
        /// </summary>
        public Task<EasyNoticeSendResponse> SendAsync(string title, Exception exception, EasyNoticeAtUser atUser = null)
        {
            var text = $"# {title}{Environment.NewLine}{exception.Message}{Environment.NewLine}{exception}";
            return SendMarkdownAsync(title, text, atUser);
        }

        /// <summary>
        /// 发送普通消息
        /// </summary>
        public Task<EasyNoticeSendResponse> SendAsync(string title, string message, EasyNoticeAtUser atUser = null)
        {
            return SendMarkdownAsync(title, message, atUser);
        }

        #region 文本消息

        /// <summary>
        /// 发送文本消息
        /// </summary>
        public Task<EasyNoticeSendResponse> SendTextAsync(string text, EasyNoticeAtUser atUser = null)
        {
            return SendTextAsync(new TextMessage(text, atUser));
        }

        /// <summary>
        /// 发送文本消息
        /// </summary>
        public Task<EasyNoticeSendResponse> SendTextAsync(TextMessage message)
        {
            return SendBaseAsync(message);
        }

        #endregion

        #region MarkDown消息

        /// <summary>
        /// 发送MarkDown消息
        /// </summary>
        public Task<EasyNoticeSendResponse> SendMarkdownAsync(string title, string text, EasyNoticeAtUser atUser = null)
        {
            return SendMarkdownAsync(new MarkdownMessage(title, text, atUser));
        }

        /// <summary>
        /// 发送MarkDown消息
        /// </summary>
        public Task<EasyNoticeSendResponse> SendMarkdownAsync(MarkdownMessage message)
        {
            return SendBaseAsync(message);
        }

        #endregion

        #region 消息卡片

        /// <summary>
        /// 发送消息卡片
        /// </summary>
        public Task<EasyNoticeSendResponse> SendActionCardAsync(string title, string text, string singleTitle, string singleURL, string btnOrientation = "0")
        {
            return SendActionCardAsync(new ActionCardMessage(title, text, singleTitle, singleURL, btnOrientation));
        }

        /// <summary>
        /// 发送消息卡片
        /// </summary>
        public Task<EasyNoticeSendResponse> SendActionCardAsync(ActionCardMessage message)
        {
            return SendBaseAsync(message);
        }

        #endregion

        /// <summary>
        /// 发送消息公共方法
        /// </summary>
        private async Task<EasyNoticeSendResponse> SendBaseAsync(MessageBase message)
        {
            try
            {
                return await IntervalHelper.IntervalExcuteAsync(async () =>
                {
                    var requestUrl = DingTalkHelper.GetRequestUrl(_dingTalkOptions.WebHook, _dingTalkOptions.Secret);
                    var response = await _httpClient.PostAsync(requestUrl, new StringContent(message.ToString(), Encoding.UTF8, "application/json"));
                    var html = await response.Content.ReadAsStringAsync();
                    var dingtalkResponse = JsonConvert.DeserializeObject<DingtalkResponse>(html);
                    if (dingtalkResponse.IsSuccess)
                    {
                        return new EasyNoticeSendResponse() { ErrCode = 0, ErrMsg = "" };
                    }
                    else
                    {
                        return new EasyNoticeSendResponse() { ErrCode = dingtalkResponse.ErrCode, ErrMsg = !string.IsNullOrEmpty(dingtalkResponse.Description) ? $"{dingtalkResponse.Description}，{dingtalkResponse.Solution}" : dingtalkResponse.ErrMsg };
                    }
                }, message.title, _noticeOptions.IntervalSeconds);
            }
            catch (Exception ex)
            {
                return new EasyNoticeSendResponse() { ErrCode = 9999, ErrMsg = $"钉钉发送消息异常:{ex.Message}" };
            }
        }

        #region 工作通知

        /// <summary>
        /// 发送工作通知
        /// </summary>
        public async Task<EasyNoticeSendResponse> SendWorkNoticeAsync(string title, string content)
        {
            return await SendWorkNoticeAsync(title, content, _dingTalkOptions.UserIds, _dingTalkOptions.DeptIds, _dingTalkOptions.ToAllUser);
        }

        /// <summary>
        /// 发送工作通知（指定接收人员）
        /// </summary>
        public async Task<EasyNoticeSendResponse> SendWorkNoticeAsync(string title, string content, List<string> userIds, List<string> deptIds = null, bool toAllUser = false)
        {
            try
            {
                // 检查工作通知配置
                if (string.IsNullOrEmpty(_dingTalkOptions.AppKey) || string.IsNullOrEmpty(_dingTalkOptions.AppSecret))
                {
                    return new EasyNoticeSendResponse() { ErrCode = 1001, ErrMsg = "工作通知配置不完整：缺少AppKey或AppSecret" };
                }

                // 获取access_token
                var accessToken = await GetAccessTokenAsync();
                if (string.IsNullOrEmpty(accessToken))
                {
                    return new EasyNoticeSendResponse() { ErrCode = 1002, ErrMsg = "获取钉钉access_token失败" };
                }

                // 构建接收人员列表
                var userIdList = new List<string>();
                if (toAllUser)
                {
                    userIdList.Add("@all");
                }
                else
                {
                    userIdList.AddRange(userIds ?? new List<string>());
                }

                // 构建部门列表
                var deptIdList = string.Join(",", deptIds ?? []);

                // 构建消息内容
                var messageContent = new
                {
                    msgtype = "text",
                    text = new
                    {
                        content = $"{title}\n{content}"
                    }
                };

                // 构建请求参数
                var requestData = new
                {
                    agent_id = _dingTalkOptions.AgentId,
                    userid_list = string.Join(",", userIdList),
                    dept_id_list = deptIdList,
                    msg = messageContent
                };

                // 发送工作通知
                var requestUrl = $"https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2?access_token={accessToken}";
                var jsonContent = System.Text.Json.JsonSerializer.Serialize(requestData);
                var httpContent = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                var response = await _httpClient.PostAsync(requestUrl, httpContent);
                var responseContent = await response.Content.ReadAsStringAsync();

                // 解析响应
                var responseObj = System.Text.Json.JsonSerializer.Deserialize<JsonElement>(responseContent);
                var errcode = responseObj.GetProperty("errcode").GetInt32();

                if (errcode == 0)
                {
                    return new EasyNoticeSendResponse() { ErrCode = 0, ErrMsg = "" };
                }
                else
                {
                    var errmsg = responseObj.GetProperty("errmsg").GetString();
                    return new EasyNoticeSendResponse() { ErrCode = errcode, ErrMsg = errmsg };
                }
            }
            catch (Exception ex)
            {
                return new EasyNoticeSendResponse() { ErrCode = 9999, ErrMsg = $"钉钉工作通知发送异常:{ex.Message}" };
            }
        }

        /// <summary>
        /// 获取钉钉access_token
        /// </summary>
        private async Task<string> GetAccessTokenAsync()
        {
            try
            {
                var requestUrl = $"https://oapi.dingtalk.com/gettoken?appkey={_dingTalkOptions.AppKey}&appsecret={_dingTalkOptions.AppSecret}";

                var response = await _httpClient.GetAsync(requestUrl);
                var responseContent = await response.Content.ReadAsStringAsync();

                var responseObj = System.Text.Json.JsonSerializer.Deserialize<JsonElement>(responseContent);
                var errcode = responseObj.GetProperty("errcode").GetInt32();

                if (errcode == 0)
                {
                    return responseObj.GetProperty("access_token").GetString();
                }
                else
                {
                    return null;
                }
            }
            catch
            {
                return null;
            }
        }

        #endregion
    }
}
