import { FlowData } from '@/components/action-panel/model';

export interface ArgsInfoV2 {
  name: string;
  apiId: string;
  description: string;

  // 响应体配置ID，用于响应验证
  responseId?: string;

  // 是否启用自动验证响应结果
  autoValidateResponse?: boolean;

  // 新版本的配置结构
  headers: FlowData[];
  params: FlowData[];
  body: FlowData[];
  outputs: FlowData[];

  // 版本标识
  version: 'v2';
  useRoot?: boolean; // 控制是否使用根节点，新增动作默认true
}

export interface ApiTemplate {
  headers?: FlowData[];
  params?: FlowData[];
  body?: FlowData[];
  response?: FlowData[];
}
