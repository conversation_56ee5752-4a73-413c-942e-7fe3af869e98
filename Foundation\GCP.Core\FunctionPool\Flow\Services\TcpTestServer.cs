using GCP.Common;
using System.Text;
using TouchSocket.Core;
using TouchSocket.Sockets;

namespace GCP.FunctionPool.Flow.Services
{
    /// <summary>
    /// TCP测试服务器（用于测试TCP请求功能）
    /// </summary>
    class TcpTestServer : DataBaseService
    {
        private static TcpService _testServer;
        private static bool _isRunning = false;

        [Function("startTcpTestServer", "启动TCP测试服务器")]
        public async Task<object> StartTestServer(int port = 7789)
        {
            try
            {
                if (_isRunning)
                {
                    return new { success = true, message = $"TCP测试服务器已在端口 {port} 运行" };
                }

                _testServer = new TcpService();
                
                var config = new TouchSocketConfig()
                    .SetListenIPHosts(port)
                    .SetTcpDataHandlingAdapter(() => new NormalDataHandlingAdapter());

                await _testServer.SetupAsync(config);

                // 设置事件处理
                _testServer.Connected += OnClientConnected;
                _testServer.Closed += OnClientDisconnected;
                _testServer.Received += OnDataReceived;

                await _testServer.StartAsync();
                _isRunning = true;

                return new { success = true, message = $"TCP测试服务器已启动，监听端口: {port}" };
            }
            catch (Exception ex)
            {
                return new { success = false, message = $"启动TCP测试服务器失败: {ex.Message}" };
            }
        }

        [Function("stopTcpTestServer", "停止TCP测试服务器")]
        public async Task<object> StopTestServer()
        {
            try
            {
                if (_testServer != null && _isRunning)
                {
                    await _testServer.StopAsync();
                    _testServer = null;
                    _isRunning = false;
                    return new { success = true, message = "TCP测试服务器已停止" };
                }
                else
                {
                    return new { success = true, message = "TCP测试服务器未运行" };
                }
            }
            catch (Exception ex)
            {
                return new { success = false, message = $"停止TCP测试服务器失败: {ex.Message}" };
            }
        }

        [Function("getTcpTestServerStatus", "获取TCP测试服务器状态")]
        public object GetTestServerStatus()
        {
            return new 
            { 
                isRunning = _isRunning,
                clientCount = _testServer?.GetIds()?.Count() ?? 0,
                message = _isRunning ? "TCP测试服务器正在运行" : "TCP测试服务器未运行"
            };
        }

        private static Task OnClientConnected(TcpSessionClient client, TouchSocketEventArgs e)
        {
            Console.WriteLine($"TCP测试服务器: 客户端 {client.Id} 已连接");
            return Task.CompletedTask;
        }

        private static Task OnClientDisconnected(TcpSessionClient client, TouchSocketEventArgs e)
        {
            Console.WriteLine($"TCP测试服务器: 客户端 {client.Id} 已断开连接");
            return Task.CompletedTask;
        }

        private static async Task OnDataReceived(TcpSessionClient client, ReceivedDataEventArgs e)
        {
            try
            {
                var receivedData = Encoding.UTF8.GetString(e.ByteBlock.ToArray());
                Console.WriteLine($"TCP测试服务器: 从客户端 {client.Id} 接收到数据: {receivedData}");

                // 简单的回显响应
                var response = $"Echo: {receivedData} (Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss})";
                var responseBytes = Encoding.UTF8.GetBytes(response);
                
                await client.SendAsync(responseBytes);
                Console.WriteLine($"TCP测试服务器: 向客户端 {client.Id} 发送响应: {response}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TCP测试服务器: 处理数据时发生错误: {ex.Message}");
            }
        }
    }
}
