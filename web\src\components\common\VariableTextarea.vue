<template>
  <div class="variable-textarea">
    <t-textarea v-model="inputValue" :placeholder="placeholder" :rows="rows" @change="handleInput" @blur="handleBlur" />
    <div class="variable-toolbar">
      <t-dropdown :options="variableOptions" @click="handleVariableSelect">
        <t-button variant="text" size="small">
          <template #icon><FunctionsIcon /></template>
          插入变量
        </t-button>
      </t-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { FunctionsIcon } from 'tdesign-icons-vue-next';

// Props
const props = defineProps<{
  modelValue?: string;
  placeholder?: string;
  variables?: any[];
  rows?: number;
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string];
}>();

// 响应式数据
const inputValue = ref(props.modelValue || '');

// 计算属性
const variableOptions = computed(() => {
  if (!props.variables || props.variables.length === 0) {
    return [{ content: '暂无可用变量', value: '', disabled: true }];
  }

  return props.variables.map((variable) => ({
    content: `${variable.name} (${variable.type})`,
    value: `{{${variable.name}}}`,
    disabled: false,
  }));
});

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    inputValue.value = newValue || '';
  },
);

// 方法
const handleInput = (value: string) => {
  inputValue.value = value;
  emit('update:modelValue', value);
};

const handleBlur = () => {
  emit('update:modelValue', inputValue.value);
};

const handleVariableSelect = (data: any) => {
  if (data.value && !data.disabled) {
    const newValue = inputValue.value + data.value;
    inputValue.value = newValue;
    emit('update:modelValue', newValue);
  }
};
</script>

<style scoped>
.variable-textarea {
  width: 100%;
  position: relative;
}

.variable-toolbar {
  margin-top: 8px;
  text-align: right;
}
</style>
