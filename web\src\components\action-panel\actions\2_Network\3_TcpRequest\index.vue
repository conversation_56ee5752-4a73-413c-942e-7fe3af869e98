<template>
  <div>
    <t-form :data="formData" label-align="left" label-width="100px" class="form-container">
      <div class="form-header">
        <t-row :gutter="[32, 24]">
          <t-col :span="6">
            <t-form-item label="名称" prop="name">
              <t-input v-model="formData.name" placeholder="请输入名称" />
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="备注" prop="description">
              <t-textarea v-model="formData.description" placeholder="请输入备注" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>

      <div class="form-body">
        <!-- TCP连接配置 -->
        <action-form-title title="连接配置" />
        <t-row :gutter="[16, 16]">
          <t-col :span="8">
            <t-form-item label="主机地址" prop="host">
              <value-input
                v-model:data-value="formData.host"
                placeholder="127.0.0.1"
                :borderless="false"
                size="medium"
                :auto-hide-edit="false"
              />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="端口" prop="port">
              <value-input
                v-model:data-value="formData.port"
                placeholder="7789"
                :borderless="false"
                size="medium"
                :auto-hide-edit="false"
              />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="超时时间(ms)" prop="timeout">
              <value-input
                v-model:data-value="formData.timeout"
                placeholder="5000"
                :borderless="false"
                size="medium"
                :auto-hide-edit="false"
              />
            </t-form-item>
          </t-col>
          <t-col :span="4">
            <t-form-item label="编码格式" prop="encoding">
              <t-select v-model:data-value="formData.encoding" :options="encodingOptions" />
            </t-form-item>
          </t-col>
        </t-row>

        <!-- 消息配置 -->
        <action-form-title title="消息配置" />
        <t-row :gutter="[16, 16]">
          <t-col :span="4">
            <t-form-item label="消息格式" prop="messageFormat">
              <t-select v-model:data-value="formData.messageFormat" :options="messageFormatOptions" />
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="消息内容" prop="message">
              <value-input
                v-model:data-value="formData.message"
                :placeholder="getMessagePlaceholder()"
                :borderless="false"
                size="medium"
                :auto-hide-edit="false"
              />
            </t-form-item>
          </t-col>
        </t-row>

        <!-- 响应配置 -->
        <action-form-title title="响应配置" />
        <t-row :gutter="[16, 16]">
          <t-col :span="4">
            <t-form-item label="等待响应" prop="waitForResponse">
              <t-switch v-model="formData.waitForResponse" />
            </t-form-item>
          </t-col>
          <t-col :span="4" v-if="formData.waitForResponse">
            <t-form-item label="响应超时(ms)" prop="responseTimeout">
              <value-input
                v-model:data-value="formData.responseTimeout"
                placeholder="10000"
                :borderless="false"
                size="medium"
                :auto-hide-edit="false"
              />
            </t-form-item>
          </t-col>
        </t-row>

        <variable-list v-if="formData.waitForResponse" v-model:data="currentStep.result" />
      </div>
    </t-form>
  </div>
</template>

<script lang="ts">
export default {
  name: 'TcpRequestActions',
};
</script>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { computed, watch } from 'vue';

import VariableList from '@/components/action-panel//VariableList.vue';
import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import ValueInput from '@/components/action-panel/ValueInput.vue';

import { useTcpRequestStore } from './store';

const actionFlowStore = useActionFlowStore();
const tcpRequestStore = useTcpRequestStore();

watch(
  () => actionFlowStore.currentStep?.id,
  () => {
    tcpRequestStore.updateState();
  },
  {
    immediate: true,
  },
);

const { currentStep } = storeToRefs(actionFlowStore);
const { args: formData } = storeToRefs(tcpRequestStore);

watch(
  () => formData.value,
  (newValue) => {
    tcpRequestStore.setArgs(newValue);
  },
  {
    deep: true,
  },
);

// 编码格式选项
const encodingOptions = [
  { label: 'UTF-8', value: 'utf8' },
  { label: 'ASCII', value: 'ascii' },
  { label: 'Base64', value: 'base64' },
];

// 消息格式选项
const messageFormatOptions = [
  { label: '文本', value: 'text' },
  { label: 'JSON', value: 'json' },
  { label: '十六进制', value: 'hex' },
];

// 根据消息格式获取占位符
const getMessagePlaceholder = () => {
  switch (formData.value.messageFormat) {
    case 'json':
      return '{"command": "test", "data": "hello"}';
    case 'hex':
      return '48656C6C6F20576F726C64';
    default:
      return 'Hello World';
  }
};
</script>

<style lang="less" scoped>
@import '@/style/form.less';
@import '@/style/table.less';

.form-header {
  width: 100%;
  padding: 16px;
}
</style>
