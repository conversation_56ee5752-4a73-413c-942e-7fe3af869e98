<template>
  <div>
    <div class="table-select-container">
      <action-form-title title="数据表">
        <template #titleSuffix>
          <t-tag v-if="formData.tableDescription" theme="primary" variant="outline">{{
            formData.tableDescription
          }}</t-tag>
        </template>
      </action-form-title>

      <data-source-table-select
        :data-source-id="props.dataSourceId"
        :table="formData.tableName"
        @select-table="handleSelectTable"
        @refresh-columns="handleRefreshColumns"
      ></data-source-table-select>
    </div>
    <div v-show="formData.tableName" class="columns-container">
      <action-form-title
        v-if="operationType !== 'Delete'"
        title="操作字段"
        :tip="operationType === 'Save' ? '是否查询：保存操作中更新可查询当前行旧数据' : ''"
      ></action-form-title>
      <t-table
        v-if="operationType !== 'Delete'"
        class="small-table"
        size="small"
        :hover="true"
        row-key="columnName"
        :columns="tableColumns"
        :data="formData.columns"
      >
        <template #columnName="{ row }">
          <mouse-input
            v-model.trim="row.columnName"
            :title="row.description"
            size="small"
            borderless
            :disabled="!row.isCustomize"
          ></mouse-input>
        </template>
        <template #dataType="{ row }">
          <value-type-select
            v-model:type="row.dataType"
            v-model:required="row.required"
            :disabled="!row.isCustomize"
          ></value-type-select>
        </template>
        <template #description="{ row }">
          <mouse-input
            v-model="row.description"
            :title="row.description"
            size="small"
            borderless
            placeholder="请输入描述"
          ></mouse-input>
        </template>
        <template #columnValue="{ row }">
          <value-input v-model:data-value="row.columnValue" :data-type="row.dataType"></value-input>
        </template>
        <template v-if="formData.columns?.length === 0" #footerSummary>
          <t-button block variant="dashed" @click="() => onClickAddColumn()">添 加 字 段</t-button>
        </template>
        <template #isUpdate="{ row }">
          <t-checkbox v-model="row.isUpdate"></t-checkbox>
        </template>
        <template #isCondition="{ row }">
          <t-checkbox v-model="row.isCondition"></t-checkbox>
        </template>
        <template #isInsert="{ row }">
          <t-checkbox v-model="row.isInsert"></t-checkbox>
        </template>
        <template #isQuery="{ row }">
          <t-checkbox v-model="row.isQuery"></t-checkbox>
        </template>
        <template #operate="{ rowIndex }">
          <t-button
            size="small"
            shape="square"
            variant="text"
            style="color: var(--td-success-color-hover)"
            @click="() => onClickAddColumn(rowIndex)"
          >
            <template #icon><add-icon /></template>
          </t-button>
          <t-button
            size="small"
            shape="square"
            variant="text"
            style="color: var(--td-error-color)"
            @click="() => onClickRemoveColumn(rowIndex)"
          >
            <template #icon><remove-icon /></template>
          </t-button>
        </template>
      </t-table>
      <action-form-title
        v-if="mustCondition"
        :title="operationType === 'Update' || operationType === 'Save' ? '更新条件' : '筛选条件'"
      ></action-form-title>
      <div v-if="mustCondition" class="condition-container">
        <condition-select
          v-model:data="formData.conditions"
          open-filter
          :columns-options="columnsOptions"
        ></condition-select>
      </div>
      <action-form-title v-if="operationType === 'Query'" title="排序"></action-form-title>
      <t-table
        v-if="operationType === 'Query'"
        class="small-table"
        size="small"
        row-key="id"
        drag-sort="row"
        :hover="true"
        :columns="sortColumns"
        :data="formData.sortColumns"
        @drag-sort="onDragSortColumn"
      >
        <template #columnName="{ row }">
          <t-select v-model="row.columnName" size="small" borderless placeholder="请选择字段">
            <t-option v-for="option in columnsOptions" :key="option.value" :value="option.value">
              <t-space>
                <div>{{ option.value }}</div>
                <t-tag v-if="option.description" size="small">{{ option.description }}</t-tag>
              </t-space>
            </t-option>
          </t-select>
        </template>
        <template #order="{ row }">
          <t-select v-model="row.order" default-value="ASC" :options="sortOptions" size="small" borderless></t-select>
        </template>
        <template v-if="formData.sortColumns.length === 0" #footerSummary>
          <t-button block variant="dashed" @click="() => onClickAddSortColumn()">添 加 排 序</t-button>
        </template>
        <template #operate="{ rowIndex }">
          <t-button
            size="small"
            shape="square"
            variant="text"
            style="color: var(--td-success-color-hover)"
            @click="() => onClickAddSortColumn(rowIndex)"
          >
            <template #icon><add-icon /></template>
          </t-button>
          <t-button
            size="small"
            shape="square"
            variant="text"
            style="color: var(--td-error-color)"
            @click="() => onClickRemoveSortColumn(rowIndex)"
          >
            <template #icon><remove-icon /></template>
          </t-button>
        </template>
      </t-table>
    </div>
  </div>
</template>
<script lang="tsx">
export default {
  name: 'DataSourceTable',
};
</script>
<script setup lang="tsx">
import { cloneDeep } from 'lodash-es';
import { AddIcon, RemoveIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, toRef } from 'vue';

import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import ConditionSelect from '@/components/action-panel/ConditionSelect.vue';
import MouseInput from '@/components/action-panel/MouseInput.vue';
import ValueInput from '@/components/action-panel/ValueInput.vue';
import ValueTypeSelect from '@/components/action-panel/ValueTypeSelect.vue';

import DataSourceTableSelect from './DataSourceTableSelect.vue';
import { ColumnInfo, DataSourceTableData } from './model';
import { getRandomId } from './utils';

const props = defineProps<{
  dataSourceId: string;
  operationType: 'Query' | 'Save' | 'Update' | 'Insert' | 'Delete';
  data: DataSourceTableData | undefined | null;
}>();
const tableColumns = computed(() => {
  let columns = [
    // { colKey: 'serial-number', width: 60, title: '序号' },
    {
      colKey: 'columnName',
      title: '字段名',
      width: 180,
    },
    {
      colKey: 'dataType',
      title: '字段类型',
      width: 135,
    },
    {
      colKey: 'columnValue',
      title: props.operationType === 'Query' ? '默认值' : '字段值',
      width: 180,
    },
    {
      colKey: 'description',
      title: '描述',
    },
  ] as any[];
  if (props.operationType === 'Query') {
    columns.push({
      colKey: 'operate',
      title: '操作',
      width: 90,
      fixed: 'right',
    });
  }

  if (props.operationType === 'Update' || props.operationType === 'Save') {
    columns.push({
      colKey: 'isUpdate',
      title: '是否更新',
      fixed: 'right',
      align: 'center',
      width: 70,
    });
  }

  if (props.operationType === 'Update' || props.operationType === 'Insert' || props.operationType === 'Save') {
    columns.push({
      colKey: 'isCondition',
      title: props.operationType === 'Update' ? '更新条件' : '唯一条件',
      fixed: 'right',
      align: 'center',
      width: 70,
    });
  }

  if (props.operationType === 'Insert' || props.operationType === 'Save') {
    columns = [
      {
        colKey: 'isInsert',
        title: '是否新增',
        fixed: 'left',
        align: 'center',
        width: 70,
      },
      ...columns,
    ];
  }

  // props.operationType === 'Query' ||
  if (props.operationType === 'Save') {
    columns = [
      {
        colKey: 'isQuery',
        title: '是否查询',
        fixed: 'left',
        align: 'center',
        width: 70,
      },
      ...columns,
    ];
  }
  return columns;
});
const sortColumns = [
  { colKey: 'serial-number', width: 60, title: '序号' },
  { colKey: 'columnName', title: '字段名', width: 180 },
  { colKey: 'order', title: '排序', width: 180 },
  {
    colKey: 'operate',
    title: '操作',
    fixed: 'right',
  },
];
const sortOptions = [
  { label: '升序', value: 'ASC' },
  { label: '降序', value: 'DESC' },
];

const columnsOptions = computed(() => {
  if (!formData.value?.columns) return [];
  return formData.value?.columns
    .filter((column) => column.columnName)
    .map((column) => ({
      label: column.description || column.columnName,
      value: column.columnName,
      description: column.description,
    }));
});

const formData = toRef(props, 'data');
// const emits = defineEmits(['update:data']);
// const formData = ref<DataSourceTableData>(
//   props.data ||
//     ({
//       tableName: '',
//       tableDescription: '',
//       columns: [],
//       sortColumns: [],
//       conditions: null,
//     } as DataSourceTableData),
// );

// watchEffect(() => {
//   formData.value = props.data || {
//     tableName: '',
//     tableDescription: '',
//     columns: [],
//     sortColumns: [],
//     conditions: null,
//   };
// });

// watch(
//   () => {
//     return formData.value;
//   },
//   (newValue) => {
//     emits('update:data', newValue);
//   },
//   {
//     deep: true,
//   },
// );
const mustCondition = computed(() => {
  return props.operationType === 'Query' || props.operationType === 'Delete';
});

const formatColumn = (column, columnValue = null) => {
  const dataType = column.memberType.replace('?', '');
  return {
    columnValue,
    columnName: column.columnName,
    description: column.description,
    dataType,
    required: !column.isNullable,
    isCustomize: false,
    isPrimaryKey: column.isPrimaryKey,
    ...(mustCondition.value
      ? {}
      : {
          isUpdate: false,
          isInsert: true,
          isCondition: false,
        }),
  };
};

const handleRefreshColumns = ({ columns }) => {
  const tempColumns = formData.value.columns;
  const insertColumnNames = [];
  const deleteColumnNames = [];
  for (const column of columns) {
    let hasColumn = false;
    let tmpColumn: ColumnInfo = null;
    for (let i = 0; i < tempColumns.length; i++) {
      const item = tempColumns[i];
      if (column.columnName === item.columnName) {
        hasColumn = true;
        tmpColumn = item;
        break;
      }
    }

    if (!hasColumn) {
      insertColumnNames.push(column.columnName);
      const newColumn = formatColumn(column);
      formData.value.columns.push(newColumn);
    } else {
      tmpColumn.isPrimaryKey = column.isPrimaryKey;
      tmpColumn.required = !column.isNullable;
      tmpColumn.dataType = column.memberType.replace('?', '');
      if (column.description) {
        tmpColumn.description = column.description;
      }
    }
  }
  const dbColumns = formData.value.columns.filter((t) => !t.isCustomize);
  if (dbColumns.length !== columns.length) {
    for (let i = dbColumns.length - 1; i >= 0; i--) {
      const item = dbColumns[i];
      const hasColumn = columns.some((t) => t.columnName === item.columnName);
      if (!hasColumn) {
        deleteColumnNames.push(item.columnName);
      }
    }
  }

  formData.value.columns = formData.value.columns.filter((t) => !deleteColumnNames.includes(t.columnName));
  if (insertColumnNames.length > 0 || deleteColumnNames.length > 0) {
    MessagePlugin.success(
      `刷新字段成功，删除字段：${deleteColumnNames.join(',') || '无'}，新增字段：${insertColumnNames.join(',') || '无'}`,
    );
  } else {
    MessagePlugin.info('字段无变更');
  }
};

const handleSelectTable = ({ tableName, description, columns }) => {
  const tempColumns = cloneDeep(formData.value.columns) as ColumnInfo[];

  formData.value.tableName = tableName;
  formData.value.tableDescription = description;
  let pkCount = 0;

  formData.value.columns = columns?.map((column) => {
    if (column.isPrimaryKey) pkCount++;
    const tempColumn = tempColumns.find((t) => t.columnName === column.columnName);
    return formatColumn(column, tempColumn?.columnValue);
  });

  if (pkCount === 0 && props.operationType !== 'Query') {
    MessagePlugin.error('请维护主键字段');
  }
};

const onClickAddColumn = (index?: number) => {
  const data = {
    columnName: '',
    description: '',
    dataType: 'string',
    required: false,
    isCustomize: true,
  };
  if (index !== undefined && index !== null && index >= 0) {
    formData.value.columns.splice(index + 1, 0, data);
  } else {
    formData.value.columns.push(data);
  }
};

const onClickRemoveColumn = (index: number) => {
  formData.value.columns.splice(index, 1);
};

const onClickAddSortColumn = (index?: number) => {
  const data = {
    id: getRandomId(),
    columnName: '',
    order: 'ASC',
  };
  if (index !== undefined && index !== null && index >= 0) {
    formData.value.sortColumns.splice(index + 1, 0, data);
  } else {
    formData.value.sortColumns.push(data);
  }
};

const onClickRemoveSortColumn = (index: number) => {
  formData.value.sortColumns.splice(index, 1);
};

const onDragSortColumn = (params) => {
  formData.value.sortColumns = params.newData;
};
</script>
<style lang="less" scoped>
@import '@/style/table.less';

.condition-container {
  background-color: #fff;
  padding: 16px;
  overflow: auto;
}
</style>
