using GCP.Iot.Interfaces;
using GCP.Iot.Models;
using Serilog;
using EasyCaching.Core;
using GCP.Common;

namespace GCP.Iot.Services
{
    /// <summary>
    /// 设备通信状态枚举
    /// </summary>
    public enum EquipmentCommunicationStatus
    {
        /// <summary>
        /// 在线状态 - 设备正常通信，批量读取正常
        /// </summary>
        Online,

        /// <summary>
        /// 离线状态 - 设备无法连接或完全无响应
        /// </summary>
        Offline,

        /// <summary>
        /// 异常状态 - 设备连接正常，但部分变量读取异常，需要分离处理
        /// </summary>
        PartialFailure
    }

    /// <summary>
    /// 管理单个设备的通信任务，包括数据轮询、缓存、事件触发和生命周期管理。
    /// 经过优化，采用批处理方式确保数据一致性，并支持智能异常变量识别和恢复。
    /// </summary>
    class EquipmentCommunicationTask : IDisposable
    {
        private readonly IDriver _driver;
        private readonly IEnumerable<EquipmentVariable> _variables;
        private readonly EquipmentTypeConfig _typeConfig;
        private Task _communicationTask;
        private CancellationTokenSource _cancellationTokenSource;
        private readonly EquipmentEventManager _eventManager;
        private readonly IEasyCachingProvider _cachingProvider;

        // 缓存键前缀
        private static string CacheKeyPrefix => "Equipment:Var:";
        // 缓存过期时间（秒）。建议从外部配置中读取。
        private const int CacheExpirationSeconds = 3600;

        // --- 连接重试逻辑的参数 ---
        private int _currentRetryDelay;
        private const int InitialRetryDelayMs = 5000; // 初始重试延迟5秒
        private const int MaxRetryDelayMs = 60000 * 1;    // 最大重试延迟1分钟

        // --- 状态相关的延迟配置 ---
        private const int OnlineDelayMs = 100; // 在线状态最小延迟
        private const int PartialFailureDelayMs = 1000; // 部分失败状态延迟1秒
        private const int OfflineDelayMs = 60000; // 离线状态延迟1分钟

        // --- 设备通信状态管理 ---
        private EquipmentCommunicationStatus _communicationStatus = EquipmentCommunicationStatus.Online;
        private int _consecutiveFailures = 0;
        private const int MaxConsecutiveFailures = 2; // 连续失败2次则认为离线
        private DateTime _lastStatusUpdate = DateTime.Now;

        // --- 异常变量管理 ---
        private readonly HashSet<string> _problematicVariableAddresses = new();
        private DateTime _lastProblematicVariablesCheck = DateTime.MinValue;
        private const int ProblematicVariablesCheckIntervalMinutes = 5; // 异常变量检查间隔

        // --- 地址绑定关系缓存 ---
        private readonly Dictionary<EquipmentVariable, List<string>> _addressBindingCache = new();
        private readonly Dictionary<string, DataTypeEnum> _expandedAddressCache = new();

        public string EquipmentId { get; }

        public string EquipmentCode { get; }
        private string EquipmentType { get; }

        public bool IsConnected => _driver.IsConnected;
        public string DriverCode => _driver.DriverCode;
        public bool IsSharedDriver { get; }

        /// <summary>
        /// 获取设备在线状态
        /// </summary>
        public bool IsOnline => _communicationStatus == EquipmentCommunicationStatus.Online;

        /// <summary>
        /// 获取设备通信状态
        /// </summary>
        public EquipmentCommunicationStatus CommunicationStatus => _communicationStatus;

        public EquipmentCommunicationTask(
            string equipmentId,
            string equipmentCode,
            string equipmentType,
            IDriver driver,
            IEnumerable<EquipmentVariable> variables,
            EquipmentTypeConfig typeConfig,
            EquipmentEventManager eventManager,
            bool isSharedDriver,
            IEasyCachingProvider cachingProvider)
        {
            EquipmentId = equipmentId;
            EquipmentCode = equipmentCode;
            EquipmentType = equipmentType;
            _driver = driver;
            _variables = variables;
            _typeConfig = typeConfig;
            _typeConfig.Equipments[equipmentId] = this;
            _eventManager = eventManager;
            IsSharedDriver = isSharedDriver;
            _cachingProvider = cachingProvider;
            _currentRetryDelay = InitialRetryDelayMs;

            InitializeCache();
            InitializeAddressBindings();
        }

        private void InitializeCache()
        {
            // 初始化时，将变量的当前值（如果有）写入缓存
            foreach (var variable in _variables)
            {
                if (variable.CurrentValue != null)
                {
                    string cacheKey = GetVariableCacheKey(variable.VarName);
                    _cachingProvider.Set(cacheKey, variable.CurrentValue, TimeSpan.FromSeconds(CacheExpirationSeconds));
                }
            }
            // 同时更新一次ALL缓存
            UpdateEquipmentValuesCache();
        }

        /// <summary>
        /// 初始化地址绑定关系缓存，避免每次读取时重复解析
        /// </summary>
        private void InitializeAddressBindings()
        {
            foreach (var variable in _variables.Where(v => !string.IsNullOrEmpty(v.Address) && !string.IsNullOrEmpty(v.DataType)))
            {
                var address = variable.Address;
                var dataType = variable.DataTypeEnum;
                var expandedList = new List<string>();

                if (address.Contains(','))
                {
                    // 处理逗号分割地址：如 "DB1,DB2,DB3"
                    var subAddresses = address.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    foreach (var addr in subAddresses)
                    {
                        var trimmedAddr = addr.Trim();
                        _expandedAddressCache[trimmedAddr] = dataType;
                        expandedList.Add(trimmedAddr);
                    }
                }
                else if (address.Contains('-') && IsNumericRangeAddress(address, dataType))
                {
                    // 处理范围地址：如 "5100-5200" (仅限非字符串类型)
                    var rangeAddresses = ExpandRangeAddress(address);
                    foreach (var addr in rangeAddresses)
                    {
                        _expandedAddressCache[addr] = dataType;
                        expandedList.Add(addr);
                    }
                }
                else
                {
                    // 单个地址
                    _expandedAddressCache[address] = dataType;
                    expandedList.Add(address);
                }

                _addressBindingCache[variable] = expandedList;
            }

            Log.Debug("设备 {EquipmentCode} 初始化地址绑定关系完成，共 {VariableCount} 个变量，展开为 {AddressCount} 个地址",
                EquipmentCode, _variables.Count(), _expandedAddressCache.Count);
        }

        /// <summary>
        /// 解析单个变量的地址绑定关系（用于动态添加的变量）
        /// </summary>
        private List<string> ParseAddressBinding(EquipmentVariable variable)
        {
            var address = variable.Address;
            var dataType = variable.DataTypeEnum;
            var expandedList = new List<string>();

            if (address.Contains(','))
            {
                // 处理逗号分割地址：如 "DB1,DB2,DB3"
                var subAddresses = address.Split(',', StringSplitOptions.RemoveEmptyEntries);
                foreach (var addr in subAddresses)
                {
                    var trimmedAddr = addr.Trim();
                    expandedList.Add(trimmedAddr);
                }
            }
            else if (address.Contains('-') && IsNumericRangeAddress(address, dataType))
            {
                // 处理范围地址：如 "5100-5200" (仅限非字符串类型)
                var rangeAddresses = ExpandRangeAddress(address);
                expandedList.AddRange(rangeAddresses);
            }
            else
            {
                // 单个地址
                expandedList.Add(address);
            }

            return expandedList;
        }

        /// <summary>
        /// 添加或更新变量的地址绑定关系（用于动态添加变量）
        /// </summary>
        public void UpdateVariableAddressBinding(EquipmentVariable variable)
        {
            if (string.IsNullOrEmpty(variable.Address))
                return;

            var expandedList = ParseAddressBinding(variable);
            _addressBindingCache[variable] = expandedList;

            // 更新展开地址缓存
            foreach (var addr in expandedList)
            {
                _expandedAddressCache[addr] = variable.DataTypeEnum;
            }

            Log.Debug("更新变量 {VarName} 的地址绑定关系，展开为 {AddressCount} 个地址",
                variable.VarName, expandedList.Count);
        }

        private string GetVariableCacheKey(string variableName)
        {
            return $"{CacheKeyPrefix}{EquipmentId}:{variableName}";
        }

        private string GetEquipmentCacheKey()
        {
            return $"{CacheKeyPrefix}{EquipmentId}:ALL";
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            _communicationTask = CommunicationLoopAsync(_cancellationTokenSource.Token);
            return Task.CompletedTask;
        }

        /// <summary>
        /// 核心通信循环，经过优化，采用批处理模式。
        /// </summary>
        private async Task CommunicationLoopAsync(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    var batchTime = DateTime.Now;
                    if (await EnsureConnectedWithRetryAsync(cancellationToken))
                    {
                        // 1. 等待一个批次的所有变量读取完成
                        var results = await ReadBatchDataAsync(_driver.SupportsBatchReading, cancellationToken);

                        // 2. 批量处理所有成功读取的结果
                        if (results.Any())
                        {
                            ProcessBatchResult(results);
                        }

                        Log.Debug("设备 {EquipmentCode} 读取数据：{EquipmentData}", EquipmentCode, results.Select(t =>
                            new[] { t.Variable.VarName, t.Result.RawValue }));
                    }
                    else
                    {
                        // 如果连接失败，更新设备离线状态
                        UpdateDeviceOnlineStatus(false);
                    }

                    // 3. 计算并执行延迟，根据设备状态动态调整
                    var processingTime = (DateTime.Now - batchTime).TotalMilliseconds;
                    var delayTime = CalculateDelayTime((int)processingTime);

                    await Task.Delay(delayTime, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                Log.Information("设备通信任务已取消: {EquipmentCode}", EquipmentCode);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "设备通信循环异常: {EquipmentCode}", EquipmentCode);
            }
        }

        /// <summary>
        /// 读取一个批次的设备数据。
        /// 使用 Task.WhenAll 等待所有变量读取任务完成。
        /// 统一处理逗号分割地址的展开和合并逻辑。
        /// </summary>
        public async Task<List<(EquipmentVariable Variable, DriverOperationResult Result)>> ReadBatchDataAsync(bool supportsBatchReading, CancellationToken cancellationToken)
        {
            var hasAddressList = _variables.Where(t => !string.IsNullOrEmpty(t.Address)).ToList();

            try
            {
                if (supportsBatchReading)
                {
                    return await ProcessSmartBatchReading(hasAddressList, cancellationToken);
                }

                // 独立读取模式
                return await ReadVariablesIndividuallyWithStatus(hasAddressList, cancellationToken);
            }
            catch (Exception ex)
            {
                // 最终异常处理，为所有变量构造错误状态
                Log.Error(ex, "设备数据读取异常: {EquipmentCode}", EquipmentCode);
                UpdateCommunicationStatus(EquipmentCommunicationStatus.Offline, ex.Message);
                return CreateErrorResultsForAllVariables(hasAddressList, ex.Message);
            }
        }

        /// <summary>
        /// 智能批量读取：根据设备状态选择不同的读取策略
        /// </summary>
        private async Task<List<(EquipmentVariable Variable, DriverOperationResult Result)>> ProcessSmartBatchReading(
            List<EquipmentVariable> hasAddressList, CancellationToken cancellationToken)
        {
            switch (_communicationStatus)
            {
                case EquipmentCommunicationStatus.Online:
                    return await ProcessOnlineReading(hasAddressList, cancellationToken);

                case EquipmentCommunicationStatus.PartialFailure:
                    return await ProcessPartialFailureReading(hasAddressList, cancellationToken);

                case EquipmentCommunicationStatus.Offline:
                default:
                    // 离线状态尝试恢复
                    return await ProcessOfflineRecovery(hasAddressList, cancellationToken);
            }
        }

        /// <summary>
        /// 在线状态的读取处理
        /// </summary>
        private async Task<List<(EquipmentVariable Variable, DriverOperationResult Result)>> ProcessOnlineReading(
            List<EquipmentVariable> hasAddressList, CancellationToken cancellationToken)
        {
            try
            {
                // 尝试批量读取
                var results = await ProcessBatchReadWithAddressExpansion(hasAddressList);
                UpdateCommunicationStatus(EquipmentCommunicationStatus.Online, "批量读取成功");
                return results;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "设备 {EquipmentCode} 批量读取失败，降级为独立读取以识别异常变量", EquipmentCode);

                // 批量读取失败，降级为独立读取来识别异常变量
                var results = await ReadVariablesIndividuallyWithStatus(hasAddressList, cancellationToken);
                AnalyzeAndUpdateStatus(results);
                return results;
            }
        }

        /// <summary>
        /// 部分失败状态的读取处理
        /// </summary>
        private async Task<List<(EquipmentVariable Variable, DriverOperationResult Result)>> ProcessPartialFailureReading(
            List<EquipmentVariable> hasAddressList, CancellationToken cancellationToken)
        {
            var results = new List<(EquipmentVariable Variable, DriverOperationResult Result)>();

            // 分离正常变量和异常变量
            var normalVariables = new List<EquipmentVariable>();
            var problematicVariables = new List<EquipmentVariable>();

            foreach (var variable in hasAddressList)
            {
                if (IsVariableProblematic(variable))
                {
                    problematicVariables.Add(variable);
                }
                else
                {
                    normalVariables.Add(variable);
                }
            }

            // 对正常变量尝试批量读取
            if (normalVariables.Count > 0)
            {
                try
                {
                    var batchResults = await ProcessBatchReadWithAddressExpansion(normalVariables);
                    results.AddRange(batchResults);
                }
                catch (Exception ex)
                {
                    Log.Warning(ex, "设备 {EquipmentCode} 正常变量批量读取失败，降级为独立读取", EquipmentCode);
                    var individualResults = await ReadVariablesIndividuallyWithStatus(normalVariables, cancellationToken);
                    results.AddRange(individualResults);

                    // 将失败的变量也标记为异常
                    MarkFailedVariablesAsProblematic(individualResults);
                }
            }

            // 对异常变量使用独立读取
            if (problematicVariables.Count > 0)
            {
                var individualResults = await ReadVariablesIndividuallyWithStatus(problematicVariables, cancellationToken);
                results.AddRange(individualResults);
            }

            // 分析结果并更新状态
            AnalyzeAndUpdateStatus(results);
            return results;
        }

        /// <summary>
        /// 离线状态的恢复处理
        /// </summary>
        private async Task<List<(EquipmentVariable Variable, DriverOperationResult Result)>> ProcessOfflineRecovery(
            List<EquipmentVariable> hasAddressList, CancellationToken cancellationToken)
        {
            // 离线状态下使用独立读取尝试恢复
            var results = await ReadVariablesIndividuallyWithStatus(hasAddressList, cancellationToken);
            AnalyzeAndUpdateStatus(results);
            return results;
        }

        /// <summary>
        /// 更新设备通信状态
        /// </summary>
        private void UpdateCommunicationStatus(EquipmentCommunicationStatus newStatus, string reason = "")
        {
            if (_communicationStatus != newStatus)
            {
                var oldStatus = _communicationStatus;
                _communicationStatus = newStatus;
                _lastStatusUpdate = DateTime.Now;

                Log.Information("设备 {EquipmentCode} 状态变更: {OldStatus} -> {NewStatus}, 原因: {Reason}",
                    EquipmentCode, oldStatus, newStatus, reason);
            }
        }

        /// <summary>
        /// 分析读取结果并更新设备状态
        /// </summary>
        private void AnalyzeAndUpdateStatus(List<(EquipmentVariable Variable, DriverOperationResult Result)> results)
        {
            if (results.Count == 0)
            {
                UpdateCommunicationStatus(EquipmentCommunicationStatus.Offline, "无读取结果");
                return;
            }

            var successCount = results.Count(r => r.Result.Status == OperationStatus.Success);
            var totalCount = results.Count;

            if (successCount == 0)
            {
                // 全部失败
                UpdateCommunicationStatus(EquipmentCommunicationStatus.Offline, "所有变量读取失败");
                _consecutiveFailures++;
            }
            else if (successCount == totalCount)
            {
                // 全部成功
                UpdateCommunicationStatus(EquipmentCommunicationStatus.Online, "所有变量读取成功");
                _consecutiveFailures = 0;

                // 检查是否可以清理异常变量列表
                CheckAndClearProblematicVariables(results);
            }
            else
            {
                // 部分成功
                UpdateCommunicationStatus(EquipmentCommunicationStatus.PartialFailure,
                    $"部分变量读取成功 ({successCount}/{totalCount})");
                _consecutiveFailures = 0;

                // 更新异常变量列表
                UpdateProblematicVariables(results);
            }
        }

        /// <summary>
        /// 判断变量是否为异常变量
        /// </summary>
        private bool IsVariableProblematic(EquipmentVariable variable)
        {
            if (_addressBindingCache.TryGetValue(variable, out var expandedAddresses))
            {
                return expandedAddresses.Any(addr => _problematicVariableAddresses.Contains(addr));
            }
            return _problematicVariableAddresses.Contains(variable.Address);
        }

        /// <summary>
        /// 更新异常变量列表
        /// </summary>
        private void UpdateProblematicVariables(List<(EquipmentVariable Variable, DriverOperationResult Result)> results)
        {
            foreach (var (variable, result) in results)
            {
                var addresses = GetVariableAddresses(variable);

                if (result.Status == OperationStatus.Success)
                {
                    // 成功的变量从异常列表中移除
                    foreach (var addr in addresses)
                    {
                        _problematicVariableAddresses.Remove(addr);
                    }
                }
                else
                {
                    // 失败的变量添加到异常列表
                    foreach (var addr in addresses)
                    {
                        _problematicVariableAddresses.Add(addr);
                    }
                }
            }

            _lastProblematicVariablesCheck = DateTime.Now;

            Log.Debug("设备 {EquipmentCode} 更新异常变量列表，当前异常变量数量: {Count}",
                EquipmentCode, _problematicVariableAddresses.Count);
        }

        /// <summary>
        /// 将失败的变量标记为异常
        /// </summary>
        private void MarkFailedVariablesAsProblematic(List<(EquipmentVariable Variable, DriverOperationResult Result)> results)
        {
            foreach (var (variable, result) in results.Where(r => r.Result.Status != OperationStatus.Success))
            {
                var addresses = GetVariableAddresses(variable);
                foreach (var addr in addresses)
                {
                    _problematicVariableAddresses.Add(addr);
                }
            }
        }

        /// <summary>
        /// 检查并清理异常变量列表
        /// </summary>
        private void CheckAndClearProblematicVariables(List<(EquipmentVariable Variable, DriverOperationResult Result)> results)
        {
            // 如果所有变量都成功，并且距离上次检查超过一定时间，清理异常变量列表
            var timeSinceLastCheck = DateTime.Now - _lastProblematicVariablesCheck;
            if (timeSinceLastCheck.TotalMinutes >= ProblematicVariablesCheckIntervalMinutes)
            {
                var clearedCount = _problematicVariableAddresses.Count;
                _problematicVariableAddresses.Clear();
                _lastProblematicVariablesCheck = DateTime.Now;

                if (clearedCount > 0)
                {
                    Log.Information("设备 {EquipmentCode} 清理异常变量列表，清理数量: {Count}",
                        EquipmentCode, clearedCount);
                }
            }
        }

        /// <summary>
        /// 获取变量的所有地址
        /// </summary>
        private List<string> GetVariableAddresses(EquipmentVariable variable)
        {
            if (_addressBindingCache.TryGetValue(variable, out var expandedAddresses))
            {
                return expandedAddresses;
            }
            return new List<string> { variable.Address };
        }

        /// <summary>
        /// 重置设备通信状态（用于变量更新后重新初始化）
        /// </summary>
        public void ResetCommunicationStatus()
        {
            _communicationStatus = EquipmentCommunicationStatus.Online;
            _consecutiveFailures = 0;
            _problematicVariableAddresses.Clear();
            _lastProblematicVariablesCheck = DateTime.MinValue;
            _lastStatusUpdate = DateTime.Now;

            Log.Information("设备 {EquipmentCode} 重置通信状态，恢复在线状态", EquipmentCode);
        }

        /// <summary>
        /// 获取设备通信状态详细信息
        /// </summary>
        public object GetCommunicationStatusInfo()
        {
            return new
            {
                Status = _communicationStatus.ToString(),
                IsOnline = IsOnline,
                ConsecutiveFailures = _consecutiveFailures,
                ProblematicVariablesCount = _problematicVariableAddresses.Count,
                ProblematicVariables = _problematicVariableAddresses.ToList(),
                LastStatusUpdate = _lastStatusUpdate,
                LastProblematicVariablesCheck = _lastProblematicVariablesCheck
            };
        }

        /// <summary>
        /// 根据设备状态计算延迟时间
        /// </summary>
        private int CalculateDelayTime(int processingTime)
        {
            int baseDelayTime = _communicationStatus switch
            {
                EquipmentCommunicationStatus.Online => Math.Max(OnlineDelayMs, _driver.MinSamplingPeriod),
                EquipmentCommunicationStatus.PartialFailure => Math.Max(PartialFailureDelayMs, _driver.MinSamplingPeriod),
                EquipmentCommunicationStatus.Offline => Math.Max(OfflineDelayMs, _driver.MinSamplingPeriod),
                _ => _driver.MinSamplingPeriod
            };

            // 减去已经消耗的处理时间，但确保最小延迟
            var adjustedDelay = Math.Max(10, baseDelayTime - processingTime);

            Log.Debug("设备 {EquipmentCode} 状态: {Status}, 基础延迟: {BaseDelay}ms, 处理时间: {ProcessingTime}ms, 实际延迟: {ActualDelay}ms",
                EquipmentCode, _communicationStatus, baseDelayTime, processingTime, adjustedDelay);

            return adjustedDelay;
        }

        /// <summary>
        /// 独立读取所有变量并返回状态信息（包括失败的变量）
        /// </summary>
        private async Task<List<(EquipmentVariable Variable, DriverOperationResult Result)>> ReadVariablesIndividuallyWithStatus(
            List<EquipmentVariable> variables, CancellationToken cancellationToken)
        {
            var results = new List<(EquipmentVariable Variable, DriverOperationResult Result)>();

            // 创建所有变量的异步读取任务
            var readTasks = variables
                .Select(async v => await ReadVariableWithStatusAsync(v, cancellationToken))
                .ToList();

            // 等待所有任务完成
            await Task.WhenAll(readTasks);

            // 收集所有结果（包括失败的）
            foreach (var task in readTasks)
            {
                var result = task.Result;
                if (result.HasValue)
                {
                    results.Add(result.Value);
                }
            }

            // 如果有成功读取的结果，更新设备在线状态
            UpdateDeviceOnlineStatus(results.Any(r => r.Result.Status == OperationStatus.Success));

            return results;
        }

        /// <summary>
        /// 读取单个变量并始终返回状态信息（即使失败也返回错误状态）
        /// </summary>
        private async Task<(EquipmentVariable Variable, DriverOperationResult Result)?> ReadVariableWithStatusAsync(
            EquipmentVariable variable, CancellationToken cancellationToken)
        {
            try
            {
                DriverOperationResult result;

                // 检查是否需要地址展开
                if (_addressBindingCache.TryGetValue(variable, out var expandedAddresses) && expandedAddresses.Count > 1)
                {
                    // 需要地址展开，根据驱动是否支持批量读取选择不同策略
                    result = await ReadExpandedAddressesAsync(variable, expandedAddresses, cancellationToken);
                }
                else
                {
                    // 单个地址，直接读取
                    result = await _driver.ReadAsync(variable.Address, variable.DataTypeEnum);
                    result.VariableId = variable.Id;
                }

                return (variable, result);
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "读取变量异常: {EquipmentCode}.{VarName}", EquipmentCode, variable.VarName);

                // 即使异常也返回错误状态，而不是 null
                var errorResult = new DriverOperationResult
                {
                    Status = OperationStatus.Failed,
                    ErrorMessage = ex.Message,
                    VariableId = variable.Id
                };

                return (variable, errorResult);
            }
        }

        /// <summary>
        /// 为所有变量创建错误结果
        /// </summary>
        private List<(EquipmentVariable Variable, DriverOperationResult Result)> CreateErrorResultsForAllVariables(
            List<EquipmentVariable> variables, string errorMessage)
        {
            return variables.Select(variable => (
                variable,
                new DriverOperationResult
                {
                    Status = OperationStatus.Failed,
                    ErrorMessage = errorMessage,
                    VariableId = variable.Id
                }
            )).ToList();
        }

        /// <summary>
        /// 统一处理批量读取，使用缓存的地址绑定关系
        /// </summary>
        private async Task<List<(EquipmentVariable Variable, DriverOperationResult Result)>> ProcessBatchReadWithAddressExpansion(List<EquipmentVariable> hasAddressList)
        {
            // 使用缓存的地址绑定关系，避免重复解析
            var expandedAddresses = new Dictionary<string, DataTypeEnum>();
            var originalToExpanded = new Dictionary<EquipmentVariable, List<string>>();

            foreach (var variable in hasAddressList)
            {
                if (_addressBindingCache.TryGetValue(variable, out var cachedExpandedList))
                {
                    // 使用缓存的绑定关系
                    originalToExpanded[variable] = cachedExpandedList;
                    foreach (var addr in cachedExpandedList)
                    {
                        if (_expandedAddressCache.TryGetValue(addr, out var dataType))
                        {
                            expandedAddresses[addr] = dataType;
                        }
                    }
                }
                else
                {
                    // 如果缓存中没有，说明是动态添加的变量，需要实时解析
                    Log.Warning("变量 {VarName} 的地址绑定关系未在缓存中找到，将实时解析", variable.VarName);
                    var dynamicExpandedList = ParseAddressBinding(variable);
                    originalToExpanded[variable] = dynamicExpandedList;
                    foreach (var addr in dynamicExpandedList)
                    {
                        expandedAddresses[addr] = variable.DataTypeEnum;
                    }
                }
            }

            // 调用驱动的BatchReadAsync方法（驱动只需处理简单的地址映射）
            var batchResult = await _driver.BatchReadAsync(expandedAddresses);

            if (batchResult.RawValue is IDictionary<string, object> expandedResults)
            {
                // 批量读取成功，更新设备在线状态
                UpdateDeviceOnlineStatus(true);

                var results = new List<(EquipmentVariable Variable, DriverOperationResult Result)>();

                // 将展开的结果合并回原始变量格式
                foreach (var kvp in originalToExpanded)
                {
                    var variable = kvp.Key;
                    var expandedAddressList = kvp.Value;

                    if (expandedAddressList.Count == 1)
                    {
                        // 单个地址，直接返回值
                        if (expandedResults.TryGetValue(expandedAddressList[0], out var singleValue))
                        {
                            var result = new DriverOperationResult
                            {
                                Status = OperationStatus.Success,
                                RawValue = singleValue
                            };
                            results.Add((variable, result));
                        }
                        else
                        {
                            Log.Warning("批量读取结果中找不到地址: {Address}", expandedAddressList[0]);
                        }
                    }
                    else
                    {
                        // 多个地址，返回数组
                        var arrayValues = new List<object>();
                        bool allFound = true;

                        foreach (var expandedAddr in expandedAddressList)
                        {
                            if (expandedResults.TryGetValue(expandedAddr, out var value))
                            {
                                arrayValues.Add(value);
                            }
                            else
                            {
                                Log.Warning("批量读取结果中找不到地址: {Address}", expandedAddr);
                                allFound = false;
                                break;
                            }
                        }

                        if (allFound)
                        {
                            var result = new DriverOperationResult
                            {
                                Status = OperationStatus.Success,
                                RawValue = arrayValues.ToArray()
                            };
                            results.Add((variable, result));
                        }
                    }
                }

                return results;
            }

            throw new CustomException($"批量读取失败: {batchResult.ErrorMessage}");
        }

        /// <summary>
        /// 判断是否为数字范围地址（仅限非字符串类型）
        /// </summary>
        private bool IsNumericRangeAddress(string address, DataTypeEnum dataType)
        {
            // 字符串类型不支持范围读取
            if (IsStringType(dataType))
                return false;

            var parts = address.Split('-');
            if (parts.Length != 2)
                return false;

            return ushort.TryParse(parts[0], out _) && ushort.TryParse(parts[1], out _);
        }

        /// <summary>
        /// 判断是否为字符串类型
        /// </summary>
        private bool IsStringType(DataTypeEnum dataType)
        {
            return dataType is DataTypeEnum.AsciiString or DataTypeEnum.Utf8String or DataTypeEnum.Gb2312String;
        }

        /// <summary>
        /// 展开范围地址为单个地址列表
        /// </summary>
        private List<string> ExpandRangeAddress(string address)
        {
            var parts = address.Split('-');
            if (parts.Length != 2)
                throw new ArgumentException($"范围地址格式错误，应为起始地址-结束地址: {address}");

            if (!ushort.TryParse(parts[0], out var startAddr) || !ushort.TryParse(parts[1], out var endAddr))
                throw new ArgumentException($"地址格式错误，地址必须为数字: {address}");

            if (endAddr <= startAddr)
                throw new ArgumentException($"结束地址必须大于起始地址: {address}");

            var addresses = new List<string>();
            for (ushort addr = startAddr; addr <= endAddr; addr++)
            {
                addresses.Add(addr.ToString());
            }
            return addresses;
        }

        /// <summary>
        /// 确保设备连接，并带有指数退避的重试逻辑。
        /// </summary>
        private async Task<bool> EnsureConnectedWithRetryAsync(CancellationToken cancellationToken)
        {
            if (_driver.IsConnected)
            {
                // 如果已连接，重置重试延迟，并返回成功
                _currentRetryDelay = InitialRetryDelayMs;
                return true;
            }

            try
            {
                await _driver.ConnectAsync();
                if (_driver.IsConnected)
                {
                    Log.Information("设备已连接: {EquipmentCode}", EquipmentCode);
                    _currentRetryDelay = InitialRetryDelayMs; // 连接成功后重置延迟
                    return true;
                }

                Log.Information("设备连接失败: {EquipmentCode}，将在 {Delay}s 后重试", EquipmentCode, _currentRetryDelay / 1000);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "设备连接异常: {EquipmentCode}，将在 {Delay}s 后重试", EquipmentCode, _currentRetryDelay / 1000);
            }

            // 连接失败，执行延迟
            await Task.Delay(_currentRetryDelay, cancellationToken);
            // 增加下一次的延迟时间（指数退避）
            _currentRetryDelay = Math.Min(_currentRetryDelay * 2, MaxRetryDelayMs);
            return false;
        }

        /// <summary>
        /// 异步读取单个变量，支持地址展开，返回可空元组。
        /// </summary>
        private async Task<(EquipmentVariable Variable, DriverOperationResult Result)?> ReadVariableAsync(
            EquipmentVariable variable, CancellationToken cancellationToken)
        {
            try
            {
                DriverOperationResult result;

                // 检查是否需要地址展开
                if (_addressBindingCache.TryGetValue(variable, out var expandedAddresses) && expandedAddresses.Count > 1)
                {
                    // 需要地址展开，根据驱动是否支持批量读取选择不同策略
                    result = await ReadExpandedAddressesAsync(variable, expandedAddresses, cancellationToken);
                }
                else
                {
                    // 单个地址，直接读取
                    result = await _driver.ReadAsync(variable.Address, variable.DataTypeEnum);
                    result.VariableId = variable.Id;
                }

                if (result.Status == OperationStatus.Success)
                {
                    return (variable, result);
                }

                Log.Debug("读取变量失败: {EquipmentCode}.{VarName}, 状态: {Status}", EquipmentCode, variable.VarName, result.Status);
                return null;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "读取变量异常: {EquipmentCode}.{VarName}", EquipmentCode, variable.VarName);
                return null;
            }
        }

        /// <summary>
        /// 读取展开的地址列表，根据驱动支持情况选择批量读取或循环读取
        /// </summary>
        private async Task<DriverOperationResult> ReadExpandedAddressesAsync(
            EquipmentVariable variable,
            List<string> expandedAddresses,
            CancellationToken cancellationToken)
        {
            if (_driver.SupportsBatchReading)
            {
                try
                {
                    // 驱动支持批量读取，使用批量读取方式
                    return await ReadExpandedAddressesBatchAsync(variable, expandedAddresses);
                }
                catch (Exception ex)
                {
                    // 批量读取失败，降级为循环读取
                    Log.Warning(ex, "变量 {VarName} 地址展开批量读取失败，降级为循环读取", variable.VarName);
                    return await ReadExpandedAddressesSequentialAsync(variable, expandedAddresses, cancellationToken);
                }
            }
            else
            {
                // 驱动不支持批量读取，使用循环读取方式
                return await ReadExpandedAddressesSequentialAsync(variable, expandedAddresses, cancellationToken);
            }
        }

        /// <summary>
        /// 使用批量读取方式读取展开的地址
        /// </summary>
        private async Task<DriverOperationResult> ReadExpandedAddressesBatchAsync(
            EquipmentVariable variable,
            List<string> expandedAddresses)
        {
            var addressDict = expandedAddresses.ToDictionary(addr => addr, _ => variable.DataTypeEnum);
            var batchResult = await _driver.BatchReadAsync(addressDict);

            if (batchResult.Status == OperationStatus.Success && batchResult.RawValue is IDictionary<string, object> expandedResults)
            {
                // 合并多个地址的结果
                var arrayValues = new List<object>();
                bool allFound = true;

                foreach (var addr in expandedAddresses)
                {
                    if (expandedResults.TryGetValue(addr, out var value))
                    {
                        arrayValues.Add(value);
                    }
                    else
                    {
                        Log.Warning("批量读取中找不到地址: {Address}", addr);
                        allFound = false;
                        break;
                    }
                }

                if (allFound)
                {
                    return new DriverOperationResult
                    {
                        Status = OperationStatus.Success,
                        RawValue = arrayValues.ToArray(),
                        VariableId = variable.Id
                    };
                }
                else
                {
                    return new DriverOperationResult
                    {
                        Status = OperationStatus.Failed,
                        ErrorMessage = "部分地址读取失败",
                        VariableId = variable.Id
                    };
                }
            }
            else
            {
                var result = batchResult;
                result.VariableId = variable.Id;
                return result;
            }
        }

        /// <summary>
        /// 使用循环读取方式读取展开的地址（用于不支持批量读取的驱动）
        /// </summary>
        private async Task<DriverOperationResult> ReadExpandedAddressesSequentialAsync(
            EquipmentVariable variable,
            List<string> expandedAddresses,
            CancellationToken cancellationToken)
        {
            var arrayValues = new List<object>();
            var failedAddresses = new List<string>();

            // 异步循环读取每个地址
            foreach (var addr in expandedAddresses)
            {
                try
                {
                    var singleResult = await _driver.ReadAsync(addr, variable.DataTypeEnum);

                    if (singleResult.Status == OperationStatus.Success)
                    {
                        arrayValues.Add(singleResult.RawValue);
                    }
                    else
                    {
                        Log.Warning("循环读取地址失败: {Address}, 状态: {Status}, 错误: {Error}",
                            addr, singleResult.Status, singleResult.ErrorMessage);
                        failedAddresses.Add(addr);
                    }
                }
                catch (Exception ex)
                {
                    Log.Warning(ex, "循环读取地址异常: {Address}", addr);
                    failedAddresses.Add(addr);
                }

                // 检查取消令牌
                if (cancellationToken.IsCancellationRequested)
                {
                    return new DriverOperationResult
                    {
                        Status = OperationStatus.Failed,
                        ErrorMessage = "读取操作被取消",
                        VariableId = variable.Id
                    };
                }
            }

            // 判断读取结果
            if (failedAddresses.Count == 0)
            {
                // 全部成功
                return new DriverOperationResult
                {
                    Status = OperationStatus.Success,
                    RawValue = arrayValues.ToArray(),
                    VariableId = variable.Id
                };
            }
            else if (arrayValues.Count > 0)
            {
                // 部分成功
                Log.Warning("变量 {VarName} 部分地址读取失败，成功: {SuccessCount}, 失败: {FailedCount}, 失败地址: {FailedAddresses}",
                    variable.VarName, arrayValues.Count, failedAddresses.Count, string.Join(",", failedAddresses));

                return new DriverOperationResult
                {
                    Status = OperationStatus.Success, // 部分成功也算成功，但会记录警告
                    RawValue = arrayValues.ToArray(),
                    VariableId = variable.Id
                };
            }
            else
            {
                // 全部失败
                return new DriverOperationResult
                {
                    Status = OperationStatus.Failed,
                    ErrorMessage = $"所有地址读取失败: {string.Join(",", failedAddresses)}",
                    VariableId = variable.Id
                };
            }
        }

        /// <summary>
        /// 批量处理一个采集周期的所有结果。
        /// 这是确保数据一致性的核心方法。
        /// </summary>
        private void ProcessBatchResult(List<(EquipmentVariable Variable, DriverOperationResult Result)> results)
        {
            bool anyChangeOccurred = false;

            // 步骤 1: 遍历所有结果，更新每个变量的内存值、独立缓存，并检查是否有任何变化
            foreach (var (variable, result) in results)
            {
                var oldValue = variable.CurrentValue;
                var newValue = result.RawValue;

                variable.UpdateValue(newValue);

                if (newValue != null)
                {
                    string cacheKey = GetVariableCacheKey(variable.VarName);
                    _cachingProvider.Set(cacheKey, newValue, TimeSpan.FromSeconds(CacheExpirationSeconds));
                }

                bool valueChanged = !variable.ChangeThreshold.HasValue && !variable.ArchivePeriod.HasValue ||
                                    (variable.ChangeThreshold.HasValue && ShouldTriggerChangeUpload(oldValue, newValue, variable.ChangeThreshold.Value));

                bool needsArchive = variable.ArchivePeriod is > 0 && (DateTime.Now - variable.LastArchiveTime).TotalMilliseconds >= variable.ArchivePeriod;

                if (valueChanged || needsArchive)
                {
                    anyChangeOccurred = true;
                    _eventManager.HandleVariableValueChanged(EquipmentId, variable.VarName, oldValue, newValue);

                    if (needsArchive)
                    {
                        variable.Archive(); // 更新归档时间
                        Log.Debug("归档变量数据: {EquipmentCode}.{VarName}, 值: {NewValue}", EquipmentCode, variable.VarName, newValue);
                    }
                }
            }

            // 步骤 2: 如果批次中任何一个变量发生了需要上报的变化，则检查是否需要触发设备级事件和更新ALL缓存
            if (anyChangeOccurred)
            {
                if (_typeConfig.ShouldTriggerEvent(EquipmentId, _driver.ArchivePeriod))
                {
                    // 在所有变量都更新后，再更新聚合的ALL缓存，保证数据一致性
                    UpdateEquipmentValuesCache();

                    // 触发设备数据整体变化事件
                    _eventManager.HandleEquipmentDataChanged(EquipmentId, EquipmentType, GetCurrentValues());
                }
            }
        }

        /// <summary>
        /// 更新设备所有变量的聚合缓存。
        /// </summary>
        private void UpdateEquipmentValuesCache()
        {
            var allValues = _variables.Where(v => v.CurrentValue != null).ToDictionary(v => v.VarName, v => v.CurrentValue);
            _cachingProvider.Set(GetEquipmentCacheKey(), allValues, TimeSpan.FromSeconds(CacheExpirationSeconds));
        }

        /// <summary>
        /// 安全地停止通信任务。
        /// </summary>
        public async Task StopAsync()
        {
            try
            {
                // 更新设备离线状态，但不清理事件处理器
                if (_communicationStatus != EquipmentCommunicationStatus.Offline)
                {
                    UpdateCommunicationStatus(EquipmentCommunicationStatus.Offline, "通信任务已停止");

                    // 更新缓存中的设备状态
                    var statusCacheKey = $"equipment_status_{EquipmentCode}_{EquipmentId}";
                    _cachingProvider.Set(statusCacheKey, false, TimeSpan.FromMinutes(10));
                }

                if (_cancellationTokenSource is { IsCancellationRequested: false })
                {
                    await _cancellationTokenSource.CancelAsync();
                    if (_communicationTask != null)
                    {
                        // 等待通信任务优雅地完成，或在5秒后超时
                        var completedTask = await Task.WhenAny(_communicationTask, Task.Delay(5000));

                        if (completedTask != _communicationTask)
                        {
                            Log.Warning("停止设备通信任务超时: {EquipmentCode}", EquipmentCode);
                        }
                    }
                }

                // 注意：不再清理事件处理器，保持订阅状态以便设备重新上线时立即生效
                Log.Debug("设备通信任务已停止，保持事件订阅状态: {EquipmentCode}", EquipmentCode);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "停止设备通信任务时发生异常: {EquipmentCode}", EquipmentCode);
            }
            finally
            {
                // 确保在任务逻辑完全结束后才断开连接
                if (!IsSharedDriver && _driver.IsConnected)
                {
                    try
                    {
                        await _driver.DisconnectAsync();
                        Log.Information("设备已断开连接: {EquipmentCode}", EquipmentCode);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "断开设备连接时发生异常: {EquipmentCode}", EquipmentCode);
                    }
                }
            }
        }

        public async Task<DriverOperationResult> WriteVariableAsync(string varName, object value)
        {
            var variable = _variables.FirstOrDefault(v => v.VarName == varName);
            if (variable != null)
            {
                return await WriteVariableAsync(variable.Address, value, variable.DataTypeEnum);
            }

            return new DriverOperationResult()
            {
                Status = OperationStatus.Failed,
                ErrorMessage = "写入失败：变量不存在。"
            };
        }

        public async Task<DriverOperationResult> WriteVariableAsync(string address, object value, DataTypeEnum dataType)
        {
            if (!await EnsureConnectedWithRetryAsync(CancellationToken.None))
            {
                return new DriverOperationResult()
                {
                    Status = OperationStatus.NotConnected,
                    ErrorMessage = "写入失败：设备未连接。"
                };
            }
            var result = await _driver.WriteAsync(address, value, dataType);
            if (result.Status == OperationStatus.Success)
            {
                var cacheKey = GetEquipmentCacheKey();
                await _cachingProvider.RemoveAsync(cacheKey);
            }
            return result;
        }

        public Dictionary<string, object> GetCurrentValues()
        {
            // 优先从聚合缓存中获取数据
            var cacheKey = GetEquipmentCacheKey();
            var cachedValues = _cachingProvider.Get<Dictionary<string, object>>(cacheKey);

            if (cachedValues.HasValue && cachedValues.Value.Count > 0)
            {
                return cachedValues.Value;
            }

            // 缓存未命中，从内存变量中获取，并回写缓存
            var values = _variables.Where(v => v.CurrentValue != null).ToDictionary(v => v.VarName, v => v.CurrentValue);
            _cachingProvider.Set(cacheKey, values, TimeSpan.FromSeconds(CacheExpirationSeconds));
            return values;
        }

        public object GetVariableValue(string variableName)
        {
            // 优先从单个变量缓存中获取
            string cacheKey = GetVariableCacheKey(variableName);
            var cachedValue = _cachingProvider.Get<object>(cacheKey);

            if (cachedValue.HasValue)
            {
                return cachedValue.Value;
            }

            // 缓存未命中，从内存变量中获取，并回写缓存
            var variable = _variables.FirstOrDefault(v => v.VarName == variableName);
            if (variable?.CurrentValue != null)
            {
                _cachingProvider.Set(cacheKey, variable.CurrentValue, TimeSpan.FromSeconds(CacheExpirationSeconds));
                return variable.CurrentValue;
            }

            return null;
        }

        /// <summary>
        /// 获取所有变量列表
        /// </summary>
        public IEnumerable<EquipmentVariable> GetVariables()
        {
            return _variables;
        }



        private bool ShouldTriggerChangeUpload(object oldValue, object newValue, double changeThreshold)
        {
            if (oldValue == null || newValue == null) return true;

            // 对于数值类型，计算变化是否超过阈值
            if (oldValue is IConvertible && newValue is IConvertible && oldValue.GetType().IsPrimitive && newValue.GetType().IsPrimitive)
            {
                try
                {
                    var oldDouble = Convert.ToDouble(oldValue);
                    var newDouble = Convert.ToDouble(newValue);

                    if (Math.Abs(oldDouble) < 1e-9) // 避免除以零
                        return Math.Abs(newDouble) > 1e-9;

                    var changePercent = Math.Abs((newDouble - oldDouble) / oldDouble);
                    return changePercent > changeThreshold;
                }
                catch
                {
                    return !oldValue.Equals(newValue); // 转换失败则按对象比较
                }
            }

            // 对于非数值类型，只要值不相等就触发
            return !oldValue.Equals(newValue);
        }

        /// <summary>
        /// 更新设备在线状态（保留兼容性，内部调用新的状态管理）
        /// </summary>
        /// <param name="isSuccess">本次操作是否成功</param>
        private void UpdateDeviceOnlineStatus(bool isSuccess)
        {
            if (isSuccess)
            {
                // 操作成功，重置失败计数器
                _consecutiveFailures = 0;
                if (_communicationStatus == EquipmentCommunicationStatus.Offline)
                {
                    UpdateCommunicationStatus(EquipmentCommunicationStatus.Online, "操作成功，设备恢复在线");

                    // 更新缓存中的设备状态
                    var statusCacheKey = $"equipment_status_{EquipmentCode}_{EquipmentId}";
                    _cachingProvider.Set(statusCacheKey, true, TimeSpan.FromMinutes(10));
                }
            }
            else
            {
                // 操作失败，增加失败计数器
                _consecutiveFailures++;
                if (_consecutiveFailures >= MaxConsecutiveFailures && _communicationStatus != EquipmentCommunicationStatus.Offline)
                {
                    UpdateCommunicationStatus(EquipmentCommunicationStatus.Offline,
                        $"连续失败次数达到阈值: {_consecutiveFailures}");

                    // 更新缓存中的设备状态
                    var statusCacheKey = $"equipment_status_{EquipmentCode}_{EquipmentId}";
                    _cachingProvider.Set(statusCacheKey, false, TimeSpan.FromMinutes(10));
                }
            }
        }

        public void Dispose()
        {
            // 只有在真正销毁设备实例时才清理事件处理器
            // 这通常发生在设备被删除或系统关闭时，而不是临时离线时
            _eventManager.ClearEquipmentHandlers(EquipmentId);

            _typeConfig.Equipments.TryRemove(EquipmentId, out _);
            _cancellationTokenSource?.Dispose();

            // 只有非共享驱动才需要释放
            if (!IsSharedDriver)
            {
                _driver?.Dispose();
            }

            Log.Debug("设备通信任务已完全释放: {EquipmentCode}", EquipmentCode);
            GC.SuppressFinalize(this);
        }
    }
}