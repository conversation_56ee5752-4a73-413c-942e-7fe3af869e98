<template>
  <div>
    <!-- 表头 -->
    <div v-if="(level || 0) === 0" class="variable-header">
      <t-row class="variable-item header-row" align="middle">
        <t-col :span="5" :xs="4">
          <div class="header-cell">名称</div>
        </t-col>
        <t-col :span="2">
          <div class="header-cell">类型</div>
        </t-col>
        <t-col :span="2">
          <div class="header-cell">值</div>
        </t-col>
        <t-col :span="2">
          <div class="header-cell">描述</div>
        </t-col>
        <t-col :span="1" :xs="2">
          <div class="header-cell">
            <span>操作</span>
            <div style="float: right">
              <!-- 不显示root时的删除所有变量按钮 -->
              <t-popconfirm
                v-if="!showRootNode && variables.length > 0"
                content="确定要删除所有变量吗？此操作不可恢复。"
                @confirm="onClickDeleteAll"
              >
                <t-tooltip content="删除所有变量">
                  <t-button size="small" shape="square" variant="text" style="color: var(--td-error-color)">
                    <template #icon><remove-icon /></template>
                  </t-button>
                </t-tooltip>
              </t-popconfirm>

              <!-- 不显示root时的整体批量更新按钮 -->
              <t-tooltip v-if="!showRootNode" content="批量更新所有变量">
                <t-button size="small" shape="square" variant="text" style="color: #999" @click="onClickBatchUpdateAll">
                  <template #icon><upload1-icon /></template>
                </t-button>
              </t-tooltip>
            </div>
          </div>
        </t-col>
      </t-row>
    </div>

    <!-- 根节点 -->
    <div v-if="(level || 0) === 0 && showRootNode" class="root-node">
      <t-row class="variable-item root-item" align="middle">
        <t-col :span="5" :xs="4">
          <t-row class="variable-name">
            <t-col flex="none">
              <t-space size="small">
                <p style="width: 0px"></p>
                <p v-if="rootNode.children && rootNode.children.length > 0" style="padding-top: 2px">
                  <t-button
                    size="small"
                    shape="square"
                    variant="text"
                    @click.prevent.stop="onClickCollapse(rootNode.id)"
                  >
                    <template #icon>
                      <caret-right-small-icon :class="collapseIconClass(rootNode.id)" />
                    </template>
                  </t-button>
                </p>
                <p v-else style="width: 24px"></p>
              </t-space>
            </t-col>
            <t-col flex="auto" :style="{ position: 'relative', overflow: 'hidden', minHeight: '28px' }">
              <mouse-input
                v-model.trim="rootNode.key"
                :title="rootNode.key"
                size="small"
                borderless
                placeholder="根节点名称"
                :disabled="true"
                :style="
                  rootNode.children && rootNode.children.length > 0
                    ? {
                        position: 'absolute',
                        top: '2px',
                        left: '0',
                        right: '0',
                        width: 'auto',
                        maxWidth: 'none',
                        height: '28px',
                      }
                    : {
                        width: '100%',
                        maxWidth: '100%',
                        top: '2px',
                      }
                "
                @change="() => onChange('key', rootNode)"
              ></mouse-input>
            </t-col>
          </t-row>
        </t-col>
        <t-col :span="2">
          <value-type-select
            v-model:type="rootNode.type"
            v-model:required="rootNode.required"
            :disabled="false"
            @change="() => onChange('type', rootNode)"
          ></value-type-select>
        </t-col>
        <t-col :span="2">
          <value-input
            v-model:data-value="rootNode.value"
            :data-type="rootNode.type"
            :disabled="false"
            @variable-change="() => onChange('value', rootNode)"
          ></value-input>
        </t-col>
        <t-col :span="2">
          <mouse-input
            v-model="rootNode.description"
            :title="rootNodeDisplayDescription"
            size="small"
            borderless
            :placeholder="rootNodeDisplayDescription"
            :disabled="true"
            @change="() => onChange('description', rootNode)"
          ></mouse-input>
        </t-col>
        <t-col :span="1" :xs="2">
          <!-- 添加按钮 -->
          <t-dropdown :options="rootAddOptions" @click="(t) => onClickAddToRoot(t.value)">
            <t-button size="small" shape="square" variant="text" style="color: var(--td-success-color-hover)">
              <template #icon><add-icon /></template>
            </t-button>
          </t-dropdown>

          <!-- 删除子节点按钮 -->
          <t-dropdown
            v-if="rootNode.type == 'object' || rootNode.type == 'array'"
            :options="rootRemoveOptions"
            @click="(t) => onClickRemoveFromRoot(t.value)"
          >
            <t-button size="small" shape="square" variant="text" style="color: var(--td-success-color-hover)">
              <template #icon><remove-icon /></template>
            </t-button>
          </t-dropdown>

          <!-- 批量更新按钮 -->
          <t-tooltip content="批量更新变量">
            <t-button
              v-if="rootNode.type == 'object' || rootNode.type == 'array'"
              size="small"
              shape="square"
              variant="text"
              style="color: #999"
              @click="onClickBatchUpdateRoot"
            >
              <template #icon><upload1-icon /></template>
            </t-button>
          </t-tooltip>
        </t-col>
      </t-row>
      <!-- 根节点的子节点 -->
      <div
        v-if="rootNode.children && rootNode.children.length > 0 && shouldShowChildren"
        v-show="!collapseIds.includes(rootNode.id)"
      >
        <VariableList
          :data="rootNode.children"
          :parent="rootNode"
          :open-customize-disabled="openCustomizeDisabled"
          :level="1"
          :on-change="onChange"
          :output-type="outputType"
          :only-value-edited="onlyValueEdited"
        ></VariableList>
      </div>
    </div>

    <div v-for="(item, index) in variables" :key="item.id">
      <t-row class="variable-item" align="middle">
        <t-col :span="5" :xs="4">
          <t-row class="variable-name">
            <t-col flex="none">
              <t-space size="small">
                <p :style="{ width: `${(level || 0) * 16}px` }"></p>
                <p v-if="item.children && item.children.length > 0" style="padding-top: 2px">
                  <t-button size="small" shape="square" variant="text" @click.prevent.stop="onClickCollapse(item.id)">
                    <template #icon>
                      <caret-right-small-icon :class="collapseIconClass(item.id)" />
                    </template>
                  </t-button>
                </p>
                <p v-else style="width: 24px"></p>
              </t-space>
            </t-col>
            <t-col flex="auto" :style="{ position: 'relative', overflow: 'hidden', minHeight: '28px' }">
              <mouse-input
                v-model.trim="item.key"
                :title="item.key"
                size="small"
                borderless
                placeholder="名称"
                :disabled="itemNameDisabled(item)"
                :style="
                  item.children && item.children.length > 0
                    ? {
                        position: 'absolute',
                        top: '2px',
                        left: '0',
                        right: '0',
                        width: 'auto',
                        maxWidth: 'none',
                        height: '28px',
                      }
                    : {
                        width: '100%',
                        maxWidth: '100%',
                        position: 'absolute',
                        top: '1px',
                      }
                "
                @change="() => onChange('key', item)"
              ></mouse-input>
            </t-col>
          </t-row>
        </t-col>
        <t-col :span="2"
          ><value-type-select
            v-model:type="item.type"
            v-model:required="item.required"
            :disabled="itemTypeDisabled(item)"
            @change="() => onChange('type', item)"
          ></value-type-select
        ></t-col>
        <t-col :span="2">
          <value-input
            v-model:data-value="item.value"
            :data-type="item.type"
            :disabled="itemValueDisabled(item)"
            @variable-change="() => onChange('value', item)"
          ></value-input
        ></t-col>
        <t-col :span="2">
          <mouse-input
            v-model="item.description"
            :title="item.description"
            size="small"
            borderless
            placeholder="描述"
            @change="() => onChange('description')"
          ></mouse-input
        ></t-col>
        <t-col v-if="!shouldHideOperations(item) || item.type == 'object' || item.type == 'array'" :span="1" :xs="2">
          <t-dropdown
            v-if="item.type == 'object' || item.type == 'array'"
            :options="addOptions"
            @click="(t) => onClickAdd(t.value, index)"
          >
            <t-button
              size="small"
              shape="square"
              variant="text"
              style="color: var(--td-success-color-hover)"
              @click="() => onClickAdd(1, index)"
            >
              <template #icon><add-icon /></template>
            </t-button>
          </t-dropdown>
          <t-tooltip v-else content="添加变量">
            <t-button
              size="small"
              shape="square"
              variant="text"
              style="color: var(--td-success-color-hover)"
              @click="() => onClickAdd(1, index)"
            >
              <template #icon><add-icon /></template>
            </t-button>
          </t-tooltip>

          <t-dropdown
            v-if="(item.type == 'object' || item.type == 'array') && canDelete(item)"
            :options="removeOptions"
            @click="(t) => onClickRemove(t.value, index)"
          >
            <t-button
              size="small"
              shape="square"
              variant="text"
              style="color: var(--td-success-color-hover)"
              @click="() => onClickRemove(1, index)"
            >
              <template #icon><remove-icon /></template>
            </t-button>
          </t-dropdown>
          <t-tooltip v-else-if="canDelete(item)" content="删除变量">
            <t-button
              size="small"
              shape="square"
              variant="text"
              style="color: var(--td-error-color)"
              @click="onClickRemove(1, index)"
            >
              <template #icon><remove-icon /></template>
            </t-button>
          </t-tooltip>
          <t-tooltip content="批量更新变量">
            <t-button
              v-if="item.type == 'object' || item.type == 'array'"
              size="small"
              shape="square"
              variant="text"
              style="color: #999"
              @click="onClickBatchUpdate(index)"
            >
              <template #icon><upload1-icon /></template>
            </t-button>
          </t-tooltip>
        </t-col>
      </t-row>
      <div
        v-if="item.children && item.children.length > 0 && shouldShowChildren"
        v-show="!collapseIds.includes(item.id)"
      >
        <VariableList
          :data="item.children"
          :parent="item"
          :open-customize-disabled="openCustomizeDisabled"
          :level="(level || 0) + 1"
          :on-change="onChange"
          :output-type="outputType"
          :only-value-edited="onlyValueEdited"
        ></VariableList>
      </div>
    </div>
    <div v-if="(level || 0) === 0 && !showRootNode && (!openCustomizeDisabled || variables?.length === 0)">
      <div v-if="variables?.length === 0" class="empty">暂无变量</div>
      <t-button
        v-if="!openCustomizeDisabled"
        size="small"
        block
        variant="dashed"
        style="margin-top: 8px"
        @click="() => onClickAdd(1, 0)"
      >
        添 加 变 量
      </t-button>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'VariableList',
};
</script>
<script setup lang="ts">
import { debounce } from 'lodash-es';
import { AddIcon, CaretRightSmallIcon, DeleteIcon, RemoveIcon, Upload1Icon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, nextTick, ref, watch, watchEffect } from 'vue';

import MouseInput from '@/components/action-panel/MouseInput.vue';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import ValueInput from '@/components/action-panel/ValueInput.vue';
import ValueTypeSelect from '@/components/action-panel/ValueTypeSelect.vue';

import { FlowData } from './model';
import { getRandomId } from './utils';
import { useVariableManagement } from './composables/useVariableReference';

const addOptions = [
  { content: '添加子节点', value: 2 },
  { content: '添加相邻节点', value: 1 },
];

const removeOptions = [
  { content: '删除子节点', value: 2 },
  { content: '删除当前节点', value: 1 },
];

const rootAddOptions = [{ content: '添加子节点', value: 2 }];
const rootRemoveOptions = [{ content: '删除子节点', value: 2 }];

const actionFlowStore = useActionFlowStore();

// 变量引用管理
const { updateVariable, deleteVariable } = useVariableManagement();

// 用于跟踪变量的旧值，以便在更新时能够正确调用 updateVariable
const variableOldValues = new Map<string, FlowData>();

// 保存变量的旧值
const saveVariableOldValue = (variable: FlowData) => {
  if (variable && variable.id) {
    // 深拷贝变量数据
    variableOldValues.set(variable.id, JSON.parse(JSON.stringify(variable)));
  }
};

// 递归保存所有变量的旧值
const saveAllVariablesOldValues = (variables: FlowData[]) => {
  variables.forEach((variable) => {
    saveVariableOldValue(variable);
    if (variable.children && variable.children.length > 0) {
      saveAllVariablesOldValues(variable.children);
    }
  });
};
const props = withDefaults(
  defineProps<{
    data?: FlowData[];
    parent?: FlowData;
    level?: number;
    openCustomizeDisabled?: boolean;
    onChange?: (type: string, item?: FlowData) => void;
    showRootNode?: boolean;
    rootNodeName?: string;
    rootNodeDescription?: string;
    outputType?: string; // 输出配置类型：list, single, dictionary, count
    onlyValueEdited?: boolean; // 是否只允许编辑值字段，用于控制编辑权限
  }>(),
  {
    showRootNode: true,
    rootNodeName: 'ROOT', // 固定为 ROOT，不可修改
    rootNodeDescription: '根节点',
    outputType: 'list', // 默认为列表类型
    onlyValueEdited: false, // 默认允许编辑所有字段
  },
);

const emits = defineEmits<{
  'update:data': [value: FlowData[]];
}>();

// const variables = toRef(props, 'data');
const variables = ref<FlowData[]>(props.data ? props.data : []);

// 根据输出类型调整显示逻辑
const shouldShowChildren = computed(() => {
  // count 和 dictionary 类型不显示子节点
  if (props.outputType === 'count' || props.outputType === 'dictionary') {
    return false;
  }
  return true;
});

// 根据输出类型调整根节点描述
const rootNodeDisplayDescription = computed(() => {
  switch (props.outputType) {
    case 'count':
      return '记录数量';
    case 'single':
      return '单条记录';
    case 'dictionary':
      return '字典结果';
    case 'list':
    default:
      return props.rootNodeDescription || '根节点';
  }
});

// 根节点定义 - 名称固定为 ROOT
const rootNode = ref<FlowData>({
  id: 'ROOT',
  key: 'ROOT',
  description: '根节点',
  type: 'object',
  required: false,
  isCustomize: false,
  children: [],
});

watchEffect(() => {
  if (props.showRootNode && (props.level || 0) === 0) {
    // 如果显示根节点，需要检查数据中是否已经有ROOT节点
    if (props.data && props.data.length > 0) {
      const existingRootNode = props.data.find((item) => item.key === 'ROOT');
      if (existingRootNode) {
        // 如果数据中已经有ROOT节点，直接使用它
        rootNode.value = { ...existingRootNode };
        rootNode.value.key = 'ROOT'; // 确保key正确
        rootNode.value.description = props.rootNodeDescription || existingRootNode.description || '根节点';
        // 不显示其他节点，因为所有数据都应该在ROOT节点内
        variables.value = [];
      } else {
        // 如果数据中没有ROOT节点，根据节点数量决定合并策略
        if (props.data.length === 1) {
          // 只有一个节点时，合并到root上
          const singleNode = props.data[0];
          rootNode.value = {
            ...singleNode,
            id: 'ROOT',
            key: 'ROOT',
            description: props.rootNodeDescription || singleNode.description || '根节点',
          };
          variables.value = []; // 清空外层变量，避免重复显示
        } else {
          // 多个节点时，放入root的子节点
          rootNode.value.key = 'ROOT';
          rootNode.value.description = rootNodeDisplayDescription.value;
          rootNode.value.children = props.data.map((item) => {
            item.required = item.required ?? false;
            return item;
          });
          variables.value = []; // 清空外层变量，避免重复显示
        }
      }
    } else {
      // 如果没有数据，重置根节点
      rootNode.value.key = 'ROOT';
      rootNode.value.description = rootNodeDisplayDescription.value;
      rootNode.value.children = [];
      variables.value = [];
    }
  } else {
    // 如果不显示根节点，正常显示数据
    // 只有在 props.data 发生变化时才更新 variables.value
    const newData = props.data ? props.data : [];
    if (JSON.stringify(variables.value) !== JSON.stringify(newData)) {
      variables.value = newData;
      variables.value.map((item) => {
        item.required = item.required ?? false;
        return item;
      });
    }
  }

  // 保存所有变量的旧值，用于变量引用管理
  if (props.showRootNode && rootNode.value.children) {
    saveAllVariablesOldValues(rootNode.value.children);
  } else {
    saveAllVariablesOldValues(variables.value);
  }
});

// 监听rootNode的变化，当ROOT节点被合并后，触发数据更新
watch(
  () => rootNode.value,
  (newRootNode, oldRootNode) => {
    if (props.showRootNode && (props.level || 0) === 0 && newRootNode.key === 'ROOT') {
      // 只有当ROOT节点的id和key都是'ROOT'，且与传入的数据不同时，才触发更新
      // 这表示已经完成了合并操作，需要将合并结果传播回父组件
      if (newRootNode.id === 'ROOT' && newRootNode.key === 'ROOT') {
        // 检查是否与props.data中的数据不同，避免无限循环
        const hasRootInProps = props.data?.find((item) => item.key === 'ROOT');
        if (!hasRootInProps || hasRootInProps.id !== 'ROOT') {
          nextTick(() => {
            emits('update:data', [newRootNode]);
          });
        }
      }
    }
  },
  { deep: true, immediate: false },
);

const collapseIds = ref([]);
const collapseIconClass = (id) => {
  return `icon-arrow${collapseIds.value.includes(id) ? '' : ' icon-arrow-active'}`;
};

const itemIdDisabled = (item: FlowData) => {
  if (!props.openCustomizeDisabled) return false;
  if (item?.isCustomize == false) {
    // 对于只允许编辑值的情况，只有value字段可以编辑
    if (props.onlyValueEdited) {
      return false; // 允许编辑value字段
    }
    // 对于完全禁用编辑的情况
    return true;
  }
  return false;
};

// 判断名称字段是否可编辑（ROOT 节点名称不可编辑）
const itemNameDisabled = (item: FlowData) => {
  // ROOT 节点名称始终不可编辑
  if (item.id === 'ROOT' || item.key === 'ROOT') return true;

  // 对于绑定响应体的情况
  if (props.openCustomizeDisabled && item?.isCustomize == false) {
    // 名称字段都不允许修改
    return true;
  }

  return false;
};

// 判断类型字段是否可编辑
const itemTypeDisabled = (item: FlowData) => {
  // 对于绑定响应体的情况
  if (props.openCustomizeDisabled && item?.isCustomize == false) {
    // 类型字段都不允许修改
    return true;
  }

  return false;
};

// 判断值字段是否可编辑
const itemValueDisabled = (item: FlowData) => {
  // 对于绑定响应体的情况
  if (props.openCustomizeDisabled && item?.isCustomize == false) {
    // 只有在允许编辑值的情况下才可以修改值字段
    return !props.onlyValueEdited;
  }

  return false;
};

// 判断是否可以删除（根节点不能删除）
const canDelete = (item: FlowData) => {
  return item.id !== 'ROOT';
};

// 判断是否应该隐藏操作按钮
const shouldHideOperations = (item: FlowData) => {
  // 对于绑定响应体的情况，隐藏所有操作按钮
  if (props.openCustomizeDisabled && item?.isCustomize == false) {
    return true;
  }
  return false;
};

const onClickAdd = (item, index) => {
  const data: FlowData = { id: getRandomId(), type: 'string', key: '', isCustomize: true, required: false };
  let parentItem = null;
  if (item === 1) {
    variables.value.splice(index + 1, 0, data);
    parentItem = props.parent;
  } else if (item === 2) {
    const current = variables.value[index];
    if (!current.children) {
      current.children = [];
    }
    current.children?.push(data);
    parentItem = current;
  }
  onChange('add', parentItem);
};

// 添加到根节点的方法
const onClickAddToRoot = (item) => {
  const data: FlowData = { id: getRandomId(), type: 'string', key: '', isCustomize: true, required: false };
  if (item === 1) {
    // 添加相邻节点（添加到根节点的子节点列表）
    if (!rootNode.value.children) {
      rootNode.value.children = [];
    }
    rootNode.value.children.push(data);
  } else if (item === 2) {
    // 添加子节点（添加到根节点的子节点）
    if (!rootNode.value.children) {
      rootNode.value.children = [];
    }
    rootNode.value.children.push(data);
  }
  onChange('add', rootNode.value);
};

// 从根节点删除的方法
const onClickRemoveFromRoot = (item) => {
  if (item === 2) {
    // 删除所有子节点
    rootNode.value.children = [];
    onChange('remove', rootNode.value);
  }
  // 注意：不允许删除根节点本身（item === 1）
};

// 根节点批量更新
const onClickBatchUpdateRoot = () => {
  batchId.value = rootNode.value.id;
  actionFlowStore.isSaveBatchVariable = false;
  actionFlowStore.showBatchVariableDialog = true;
  actionFlowStore.currentVariableData = rootNode.value;
};

// 不显示root时的整体批量更新
const onClickBatchUpdateAll = () => {
  // 创建一个虚拟的根节点来包含所有变量
  const virtualRoot: FlowData = {
    id: 'VIRTUAL_ROOT',
    key: 'ROOT',
    description: '根节点',
    type: 'object',
    required: false,
    isCustomize: false,
    children: JSON.parse(JSON.stringify(variables.value)), // 深拷贝确保不直接引用
  };

  batchId.value = virtualRoot.id;
  actionFlowStore.isSaveBatchVariable = false;
  actionFlowStore.showBatchVariableDialog = true;
  actionFlowStore.currentVariableData = virtualRoot;
};

// 不显示root时的删除所有变量
const onClickDeleteAll = () => {
  variables.value.splice(0, variables.value.length); // 使用 splice 确保响应式更新
  onChange('removeAll', null);
  MessagePlugin.success('已删除所有变量');
};

const onClickRemove = (item, index) => {
  let parentItem = null;
  let removedItem = null;

  if (item === 1) {
    // 删除当前节点
    removedItem = variables.value[index];
    variables.value.splice(index, 1);
    parentItem = props.parent;
  } else if (item === 2) {
    // 删除子节点
    const current = variables.value[index];
    if (current.children && current.children.length > 0) {
      // 如果有子节点，需要逐个删除子节点的引用
      current.children.forEach((child) => {
        onChange('remove', child);
      });
    }
    current.children = [];
    parentItem = current;
    return; // 子节点删除已经在上面的循环中处理了
  }

  // 只有删除当前节点时才调用 onChange
  if (removedItem) {
    onChange('remove', removedItem);
  }
};

const batchId = ref('');
const onClickBatchUpdate = (index) => {
  const data = variables.value[index];
  batchId.value = data.id;
  actionFlowStore.isSaveBatchVariable = false;
  actionFlowStore.showBatchVariableDialog = true;
  actionFlowStore.currentVariableData = data;
};

watch(
  () => actionFlowStore.isSaveBatchVariable,
  (newVal, oldVal) => {
    if (newVal !== oldVal && newVal && actionFlowStore.currentVariableData?.id === batchId.value) {
      if (batchId.value === 'VIRTUAL_ROOT') {
        // 处理虚拟根节点的批量更新
        // 将更新后的子节点同步回variables
        const newChildren = actionFlowStore.currentVariableData?.children || [];
        // 清空原数组并添加新数据，确保响应式更新
        variables.value.splice(0, variables.value.length, ...newChildren);
        onChange('batch', null);
      } else if (batchId.value === 'ROOT') {
        // 处理真实根节点的批量更新
        onChange('batch', rootNode.value);
      } else {
        // 处理普通变量的批量更新
        const item = variables.value.find((t) => t.id === batchId.value);
        onChange('batch', item);
      }
      // 重置批量更新状态
      actionFlowStore.isSaveBatchVariable = false;
    }
  },
);

const onChange = debounce((type, item?: FlowData) => {
  if (type !== 'description' && item) {
    item.isCustomize = true;
  }

  // 处理变量引用管理
  if (item && item.id) {
    const oldValue = variableOldValues.get(item.id);

    if (type === 'remove') {
      // 删除变量时，调用 deleteVariable
      if (oldValue) {
        deleteVariable(oldValue);
        variableOldValues.delete(item.id);
      }
    } else if (type === 'key' || type === 'type' || type === 'description') {
      // 更新变量的关键属性时，调用 updateVariable
      if (oldValue) {
        updateVariable(oldValue, item);
        // 更新保存的旧值
        saveVariableOldValue(item);
      }
    } else if (type === 'add') {
      // 添加新变量时，保存其初始值
      saveVariableOldValue(item);
    }
  }

  if (props.onChange) {
    props.onChange(type, item);
  }

  // 触发数据更新事件
  if (props.showRootNode && (props.level || 0) === 0) {
    // 如果显示根节点，返回包含ROOT节点的完整数据
    // 这样可以保持ROOT节点的类型信息
    emits('update:data', [rootNode.value]);
  } else {
    // 如果不显示根节点，返回当前变量数据
    emits('update:data', variables.value);
  }
}, 300);

const onClickCollapse = (id: string) => {
  if (collapseIds.value.includes(id)) {
    collapseIds.value = collapseIds.value.filter((t) => t !== id);
  } else {
    collapseIds.value.push(id);
  }
};
</script>
<style lang="less" scoped>
.empty {
  padding: 32px 20px;
  text-align: center;
  color: #999;
  font-size: 16px;
}
.icon-arrow {
  transition: all 0.2s cubic-bezier(0.38, 0, 0.24, 1);

  &.icon-arrow-active {
    transform: rotate(90deg);
  }
}
.variable-header {
  .header-row {
    background-color: var(--td-bg-color-container);
    border-bottom: 1px solid var(--td-border-level-2-color);
    height: 32px;

    .header-cell {
      padding: 5px 8px;
      color: var(--td-text-color-secondary);
      font-size: 12px;
      font-weight: normal;
    }
  }
}

.root-node {
  .root-item {
    background-color: var(--td-bg-color-container-select);
    border-bottom: 1px solid var(--td-border-level-2-color);
    height: 32px;

    .variable-name {
      flex-wrap: nowrap;
      margin-right: 8px;
      overflow: hidden;

      :deep(.t-input) {
        max-width: 100%;
      }

      :deep(.t-input__inner) {
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.variable-item {
  border-bottom: 1px solid var(--td-border-level-2-color);
  height: 32px;
  .variable-name {
    flex-wrap: nowrap;
    margin-right: 8px;
    overflow: hidden;

    :deep(.t-input) {
      max-width: 100%;
    }

    :deep(.t-input__inner) {
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &:hover {
    background-color: var(--td-bg-color-container-hover);
  }
}
</style>
