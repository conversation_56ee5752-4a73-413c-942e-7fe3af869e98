﻿using GCP.Common;
using GCP.Iot.Interfaces;
using GCP.Iot.Models;
using IoTClient;
using IoTClient.Clients.Modbus;
using IoTClient.Enums;
using IoTClient.Models;
using Serilog;
using System.IO.Ports;
using System.Text;
using DataTypeEnum = GCP.Iot.Models.DataTypeEnum;
using IotDataTypeEnum = IoTClient.Enums.DataTypeEnum;

namespace GCP.Iot.ModBus
{
    [DriverInfo("提供与 Modbus 从站兼容设备和应用程序的连接。 支持串行 ASCII、串行 RTU、TCP 和 UDP 协议。")]
    public class ModBusDriver : IDriver
    {
        private IModbusClient _client;

        public ModBusDriver()
        {
        }

        public bool IsConnected => _client is { Connected: true };

        private ILogger _logger;
        public ILogger Logger
        {
            get => _logger;
            set
            {
                _logger = value;
                _logger?.Information($"ModBus驱动初始化完成");
            }
        }

        public string DriverCode => "ModBus";
        public bool SupportsBatchReading => true;

        #region 配置参数

        [DriverParameter("最小采集周期(毫秒)")]
        public int MinSamplingPeriod { get; set; } = 1000;

        [DriverParameter("通信方式，Tcp|Rtu|RtuOnTcp|Ascii")]
        public MasterType MasterType { get; set; } = MasterType.Tcp;

        [DriverParameter("IP地址")]
        public string IpAddress { get; set; } = "127.0.0.1";

        [DriverParameter("端口号")]
        public int Port { get; set; } = 502;

        [DriverParameter("串口名，如COM4|如COM5")]
        public string PortName { get; set; } = "COM1";

        [DriverParameter("波特率，如1200|4800|9600|14400")]
        public int BaudRate { get; set; } = 9600;

        [DriverParameter("数据位")]
        public int DataBits { get; set; } = 8;

        [DriverParameter("停止位，0|1|2|3")]
        public StopBits StopBits { get; set; } = StopBits.One;

        [DriverParameter("校验位，None|Even|Odd")]
        public Parity Parity { get; set; } = Parity.None;

        [DriverParameter("站号")]
        public byte StationNumber { get; set; } = 1;

        [DriverParameter("字符串颠倒")]
        public bool StringReverse { get; set; } = false;

        [DriverParameter("大小端，ABCD|BADC|CDAB|DCBA")]
        public EndianFormat EndianFormat { get; set; } = EndianFormat.ABCD;

        [DriverParameter("超时时间ms")]
        public int Timeout { get; set; } = 3000;

        [DriverParameter("存档周期(毫秒)")]
        public int? ArchivePeriod { get; set; }

        #endregion
        public async Task<bool> ConnectAsync()
        {
            try
            {
                _client?.Close();

                switch (MasterType)
                {
                    case MasterType.Tcp:
                        _client = new ModbusTcpClient(IpAddress, Port, Timeout, EndianFormat);
                        break;
                    case MasterType.Rtu:
                        _client = new ModbusRtuClient(PortName, BaudRate, DataBits, StopBits, Parity, Timeout, EndianFormat);
                        break;
                    case MasterType.RtuOnTcp:
                        _client = new ModbusRtuOverTcpClient(IpAddress, Port, Timeout, EndianFormat);
                        break;
                    case MasterType.Ascii:
                        _client = new ModbusAsciiClient(PortName, BaudRate, DataBits, StopBits, Parity, Timeout, EndianFormat);
                        break;
                    default:
                        throw new ArgumentException("不支持的ModBus通信方式");
                }

                var result = _client.Open();
                if (!result.IsSucceed)
                {
                    throw result.Exception;
                }

                _logger?.Information("成功连接到ModBus设备: {S}，通信方式：{MasterType1}", MasterType is MasterType.Rtu or MasterType.Ascii ? PortName : $"{IpAddress}:{Port}", MasterType);
                return true;
            }
            catch (Exception)
            {
                return await Task.FromResult(false);
            }
        }

        public async Task<bool> DisconnectAsync()
        {
            try
            {
                _client?.Close();

                _client = null;

                _logger?.Information("已断开与ModBus设备的连接：{S}", MasterType is MasterType.Rtu or MasterType.Ascii ? PortName : $"{IpAddress}:{Port}");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.Error(ex, "断开ModBus设备连接失败：{S}", MasterType is MasterType.Rtu or MasterType.Ascii ? PortName : $"{IpAddress}:{Port}");
                return await Task.FromResult(false);
            }
        }

        public void Dispose()
        {
            try
            {
                _client?.Close();

                GC.SuppressFinalize(this);
                _logger?.Information("ModBus驱动已释放资源");
            }
            catch (Exception ex)
            {
                _logger?.Error(ex, "释放ModBus驱动资源时发生错误");
            }
        }

        [DriverMethod("读ModBus", description: "读ModBus地址")]
        public Task<DriverOperationResult> ReadAsync(string address, DataTypeEnum dataType)
        {
            var result = new DriverOperationResult
            {
                Status = OperationStatus.Success,
                VariableId = address,
            };

            try
            {
                if (IsConnected)
                {
                    dynamic iotResult = null;

                    switch (dataType)
                    {
                        case DataTypeEnum.Uint16:
                            iotResult = address.Contains('.') ? _client.ReadUInt16Bit(address, StationNumber) : _client.ReadUInt16(address, StationNumber);
                            break;
                        case DataTypeEnum.Int16:
                            iotResult = address.Contains('.') ? _client.ReadInt16Bit(address, StationNumber) : _client.ReadInt16(address, StationNumber);
                            break;
                        case DataTypeEnum.Uint32:
                            iotResult = _client.ReadUInt32(address, StationNumber);
                            break;
                        case DataTypeEnum.Int32:
                            iotResult = _client.ReadInt32(address, StationNumber);
                            break;
                        case DataTypeEnum.Float:
                            iotResult = _client.ReadFloat(address, StationNumber);
                            break;
                        case DataTypeEnum.Uint64:
                            iotResult = _client.ReadUInt64(address, StationNumber);
                            break;
                        case DataTypeEnum.Int64:
                            iotResult = _client.ReadInt64(address, StationNumber);
                            break;
                        case DataTypeEnum.Double:
                            iotResult = _client.ReadDouble(address, StationNumber);
                            break;
                        case DataTypeEnum.AsciiString:
                            iotResult = ReadString(address, StationNumber, Encoding.ASCII);
                            break;
                        case DataTypeEnum.Utf8String:
                            iotResult = ReadString(address, StationNumber, Encoding.UTF8);
                            break;
                        case DataTypeEnum.Gb2312String:
                            iotResult = ReadString(address, StationNumber, Encoding.GetEncoding("GB2312"));
                            break;
                    }

                    if (iotResult == null)
                    {
                        throw new CustomException("不支持的ModBus数据类型");
                    }

                    if (iotResult.IsSucceed)
                    {
                        result.RawValue = iotResult.Value;
                    }
                    else
                    {
                        throw iotResult.Excepion;
                    }
                }
                else
                {
                    result.Status = OperationStatus.NotConnected;
                    result.ErrorMessage = "未连接到ModBus设备";
                }
            }
            catch (Exception ex)
            {
                var message = $"读取地址 {address} 失败";
                _logger?.Error(ex, message + ex.Message);
                result.Status = OperationStatus.Failed;
                result.ErrorMessage = message;
            }
            return Task.FromResult(result);
        }

        [DriverMethod("批量读ModBus", description: "批量读ModBus节点")]
        public async Task<DriverOperationResult> BatchReadAsync(Dictionary<string, DataTypeEnum> addresses)
        {
            var result = new DriverOperationResult
            {
                Status = OperationStatus.Success,
            };

            try
            {
                if (IsConnected)
                {
                    var numAddresses = addresses
                        .Where(t => t.Value is not DataTypeEnum.AsciiString and not DataTypeEnum.Utf8String
                            and not DataTypeEnum.Gb2312String)
                        .Select(t => new ModbusInput()
                        {
                            Address = t.Key,
                            DataType = GetDataType(t.Value),
                            StationNumber = StationNumber,
                            FunctionCode = 3
                        }).ToList();
                    var iotResult = _client.BatchRead(numAddresses);

                    if (!iotResult.IsSucceed)
                        throw new CustomException($"批量读ModBus失败 地址：{numAddresses.Select(t => t.Address)} 站号：{StationNumber} 功能码：3。" + iotResult.Err);

                    var values = iotResult.Value.ToDictionary(item => item.Address.ToString(), item => item.Value);

                    var textTasks = addresses.Where(t => t.Value is DataTypeEnum.AsciiString or DataTypeEnum.Utf8String or DataTypeEnum.Gb2312String).Select(t => ReadAsync(t.Key, t.Value)).ToList();

                    await Task.WhenAll(textTasks);

                    foreach (var task in textTasks)
                    {
                        values.Add(task.Result.VariableId, task.Result.RawValue);
                    }

                    result.RawValue = values;
                }
                else
                {
                    result.Status = OperationStatus.NotConnected;
                    result.ErrorMessage = "未连接到ModBus设备";
                }
            }
            catch (Exception ex)
            {
                var message = $"读取地址 失败";
                _logger?.Error(ex, message + "，" + ex.Message);
                result.Status = OperationStatus.Failed;
                result.ErrorMessage = message;
            }
            return result;
        }



        private IotDataTypeEnum GetDataType(DataTypeEnum dataType)
        {
            return dataType switch
            {
                DataTypeEnum.Bool => IotDataTypeEnum.Bool,
                DataTypeEnum.Byte => IotDataTypeEnum.Byte,
                DataTypeEnum.Uint16 => IotDataTypeEnum.UInt16,
                DataTypeEnum.Int16 => IotDataTypeEnum.Int16,
                DataTypeEnum.Uint32 => IotDataTypeEnum.UInt32,
                DataTypeEnum.Int32 => IotDataTypeEnum.Int32,
                DataTypeEnum.Float => IotDataTypeEnum.Float,
                DataTypeEnum.Uint64 => IotDataTypeEnum.UInt64,
                DataTypeEnum.Int64 => IotDataTypeEnum.Int64,
                DataTypeEnum.Double => IotDataTypeEnum.Double,
                _ => throw new CustomException("不支持的ModBus数据类型 " + dataType)
            };
        }

        /// <summary>
        /// 对 Modbus 返回的字节数组进行字节序反转，以修正颠倒的字符串。
        /// Modbus通常将字符串存储在16位寄存器中，但字节顺序可能不符合标准ASCII编码顺序。
        /// </summary>
        /// <param name="rawData">从 Modbus 设备读取到的原始字节数组。</param>
        /// <returns>经过字节序校正后的字符串。</returns>
        private static byte[] GetWithByteSwap(byte[] rawData)
        {
            if (rawData == null || rawData.Length == 0)
            {
                return null;
            }

            // 确保字节数组的长度是偶数，如果不是，最后一个字节将被忽略
            var length = rawData.Length;
            if (length % 2 != 0)
            {
                length--;
            }

            // 创建一个新的字节数组用于存放交换后的字节
            var swappedData = new byte[rawData.Length];

            for (var i = 0; i < length; i += 2)
            {
                // 交换每两个字节的位置
                swappedData[i] = rawData[i + 1];
                swappedData[i + 1] = rawData[i];
            }

            // 如果原始数据长度为奇数，将最后一个字节复制过来
            if (rawData.Length % 2 != 0)
            {
                swappedData[rawData.Length - 1] = rawData[^1];
            }

            return swappedData;
        }

        /// <summary>
        /// (写入时使用) 将标准字符串转换为两两字节交换后的字节数组，以写入Modbus设备。
        /// </summary>
        /// <param name="bytes">要写入的字节。</param>
        /// <param name="length">期望输出的字节数组总长度（对应Modbus的寄存器数量 * 2）。如果字符串不够长，会用空字符'\0'补齐。</param>
        /// <param name="isSwapped"></param>
        /// <returns>经过字节序交换后，可直接用于写入的字节数组。</returns>
        static byte[] CreateByteArrayFromString(byte[] bytes, int length, bool isSwapped = false)
        {
            // 创建一个指定长度的字节数组，默认填充为0（即'\0'）
            var buffer = new byte[length];

            // 确保不会溢出
            var bytesToCopy = Math.Min(bytes.Length, length);
            Array.Copy(bytes, buffer, bytesToCopy);

            if (!isSwapped) return buffer;

            // 对整个缓冲区进行字节交换
            // 注意：这里我们交换整个目标长度的缓冲区，以确保填充的'\0'也参与交换，
            // 这样可以正确地清空或覆写寄存器。
            for (var i = 0; i < length - (length % 2); i += 2)
            {
                (buffer[i], buffer[i + 1]) = (buffer[i + 1], buffer[i]);
            }

            return buffer;
        }

        private Result<string> ReadString(string address, byte stationNumber, Encoding encoding = null,
            ushort? readLength = null)
        {
            encoding ??= Encoding.ASCII;
            ushort length = 0;
            if (readLength == null)
            {
                if (address.Contains('-'))
                {
                    var addressParts = address.Split('-');
                    if (addressParts.Length == 2)
                    {
                        var address1 = ushort.Parse(addressParts[0]);
                        var address2 = ushort.Parse(addressParts[1]);
                        length = (ushort)(address2 - address1);
                        address = address1.ToString();
                    }
                    else
                    {
                        throw new CustomException("地址格式错误，应为地址1-地址2");
                    }
                }
                else
                {
                    readLength = 20;
                }
            }

            if (length == 0 && readLength != null)
                length = (ushort)Math.Ceiling(readLength.Value / 2.0);

            var result1 = _client.Read(address, stationNumber, 3, length, false);
            var result2 = new Result<string>(result1);
            if (result2.IsSucceed)
            {
                var data = result1.Value.Reverse().ToArray();
                if (StringReverse)
                {
                    data = GetWithByteSwap(data);
                }

                result2.Value = encoding.GetString(data)?.TrimEnd('\0');
            }
            return result2;
        }

        private Result WriteString(string address, string value, byte stationNumber, Encoding encoding = null)
        {
            encoding ??= Encoding.ASCII;
            var length = 0;
            var bytes = encoding.GetBytes(value);

            if (address.Contains('-'))
            {
                var addressParts = address.Split('-');
                if (addressParts.Length == 2)
                {
                    var address1 = ushort.Parse(addressParts[0]);
                    var address2 = ushort.Parse(addressParts[1]);
                    length = address2 - address1;
                    address = address1.ToString();
                }
                else
                {
                    throw new CustomException("地址格式错误，应为地址1-地址2");
                }
            }

            if (length > 0)
            {
                length *= 2;
                bytes = CreateByteArrayFromString(bytes, length, StringReverse);

                if (bytes.Length > length)
                    throw new CustomException($"写入的字节数超过地址范围，地址：{address} 长度：{length} 写入字节数：{bytes.Length} 值：{value}");
            }

            return _client.Write(address, bytes, stationNumber);
        }

        public Task<DriverOperationResult> WriteAsync(string address, object value, DataTypeEnum dataType)
        {
            var result = new DriverOperationResult
            {
                Status = OperationStatus.Success,
            };

            try
            {
                if (IsConnected)
                {
                    dynamic iotResult = null;
                    switch (dataType)
                    {
                        case DataTypeEnum.Uint16:
                            iotResult = _client.Write(address, value.Parse<ushort>(), StationNumber);
                            break;
                        case DataTypeEnum.Int16:
                            iotResult = _client.Write(address, value.Parse<short>(), StationNumber);
                            break;
                        case DataTypeEnum.Uint32:
                            iotResult = _client.Write(address, value.Parse<uint>(), StationNumber);
                            break;
                        case DataTypeEnum.Int32:
                            iotResult = _client.Write(address, value.Parse<int>(), StationNumber);
                            break;
                        case DataTypeEnum.Float:
                            iotResult = _client.Write(address, value.Parse<float>(), StationNumber);
                            break;
                        case DataTypeEnum.Uint64:
                            iotResult = _client.Write(address, value.Parse<ulong>(), StationNumber);
                            break;
                        case DataTypeEnum.Int64:
                            iotResult = _client.Write(address, value.Parse<long>(), StationNumber);
                            break;
                        case DataTypeEnum.Double:
                            iotResult = _client.Write(address, value.Parse<double>(), StationNumber);
                            break;
                        case DataTypeEnum.AsciiString:
                            iotResult = WriteString(address, value.Parse<string>(), StationNumber, Encoding.ASCII);
                            break;
                        case DataTypeEnum.Utf8String:
                            iotResult = WriteString(address, value.Parse<string>(), StationNumber, Encoding.UTF8);
                            break;
                        case DataTypeEnum.Gb2312String:
                            iotResult = WriteString(address, value.Parse<string>(), StationNumber, Encoding.GetEncoding("GB2312"));
                            break;
                    }

                    if (iotResult == null)
                    {
                        throw new CustomException("不支持的ModBus数据类型");
                    }

                    if (!iotResult.IsSucceed)
                    {
                        throw iotResult.Excepion;
                    }
                }
                else
                {
                    result.Status = OperationStatus.NotConnected;
                    result.ErrorMessage = "未连接到ModBus设备";
                }
            }
            catch (Exception ex)
            {
                var message = $"写入地址 {address} 失败";
                _logger?.Error(ex, message + ex.Message);
                result.Status = OperationStatus.Failed;
                result.ErrorMessage = message;
            }
            return Task.FromResult(result);
        }
    }
}
