using System.Collections.Concurrent;
using GCP.Common;
using Serilog;

namespace GCP.Eventbus.Infrastructure
{
    class MessageBusManager : IMessageBusManager, IAsyncDisposable
    {
        private readonly IMessageBusFactory _messageBusFactory;
        private readonly ConcurrentDictionary<string, IMessageBus> _messageBuses;
        private readonly ConcurrentDictionary<string, IMessageConsumer> _consumers;
        private volatile bool _isDisposed;

        public IReadOnlyDictionary<string, IMessageBus> MessageBuses => _messageBuses;
        public IReadOnlyDictionary<string, IMessageConsumer> Consumers => _consumers;

        public MessageBusManager(
            IMessageBusFactory messageBusFactory)
        {
            _messageBusFactory = messageBusFactory;
            _messageBuses = new ConcurrentDictionary<string, IMessageBus>();
            _consumers = new ConcurrentDictionary<string, IMessageConsumer>();
        }

        public async Task<IMessageBus> AddMessageBusAsync(MessageBusOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            // 先检查是否已存在
            if (_messageBuses.TryGetValue(options.Name, out var existingBus))
            {
                return existingBus;
            }

            // 创建新的消息总线
            var messageBus = await _messageBusFactory.CreateAsync(options);

            // 尝试原子性添加
            if (_messageBuses.TryAdd(options.Name, messageBus))
            {
                try
                {
                    await messageBus.ConnectAsync(cancellationToken);
                    Log.Information("添加 {Type} 消息总线成功: {Name}", options.Type, options.Name);
                    return messageBus;
                }
                catch
                {
                    // 如果连接失败，移除已添加的消息总线
                    _messageBuses.TryRemove(options.Name, out _);
                    await messageBus.DisposeAsync();
                    throw;
                }
            }

            // 如果添加失败（可能其他线程已经添加了），释放创建的实例并返回现有的
            await messageBus.DisposeAsync();
            return _messageBuses.TryGetValue(options.Name, out var concurrentBus)
                ? concurrentBus
                : throw new InvalidOperationException($"添加消息总线失败: {options.Name}");
        }

        public async Task RemoveMessageBusAsync(string name, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            // 首先移除使用此消息总线的所有消费者
            var consumersToRemove = _consumers.Values
                .Where(c => c.Options.BusName == name)
                .ToList();

            // 并发移除所有相关消费者
            var removeTasks = consumersToRemove.Select(consumer =>
                RemoveConsumerAsync(consumer.Name, cancellationToken));

            await Task.WhenAll(removeTasks);

            // 移除消息总线
            if (_messageBuses.TryRemove(name, out var messageBus))
            {
                try
                {
                    await messageBus.DisposeAsync();
                    Log.Information("移除消息总线成功: {Name}", name);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "释放消息总线时发生错误: {Name}", name);
                    throw;
                }
            }
        }

        public async Task<IMessageConsumer> AddConsumerAsync(ConsumerOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            // 先检查是否已存在
            if (_consumers.TryGetValue(options.Name, out var existingConsumer))
            {
                return existingConsumer;
            }

            // 检查消息总线是否存在
            if (!_messageBuses.TryGetValue(options.BusName, out var messageBus))
            {
                throw new InvalidOperationException($"未找到消息总线: {options.BusName}");
            }

            // 创建新的消费者
            var consumer = new MessageConsumer(options, messageBus);

            // 尝试原子性添加
            if (_consumers.TryAdd(options.Name, consumer))
            {
                try
                {
                    if (options.IsEnabled)
                    {
                        await consumer.StartAsync(cancellationToken);
                    }
                    //Log.Information("添加消费者成功: {Name}", options.Name);
                    return consumer;
                }
                catch
                {
                    // 如果启动失败，移除已添加的消费者
                    _consumers.TryRemove(options.Name, out _);
                    await consumer.DisposeAsync();
                    throw;
                }
            }

            // 如果添加失败（可能其他线程已经添加了），释放创建的实例并返回现有的
            await consumer.DisposeAsync();
            return _consumers.TryGetValue(options.Name, out var concurrentConsumer)
                ? concurrentConsumer
                : throw new InvalidOperationException($"添加消费者失败: {options.Name}");
        }

        public async Task RemoveConsumerAsync(string name, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            if (_consumers.TryRemove(name, out var consumer))
            {
                try
                {
                    await consumer.DisposeAsync();
                    Log.Information("移除消费者成功: {Name}", name);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "释放消费者时发生错误: {Name}", name);
                    throw;
                }
            }
        }

        /// <summary>
        /// 强制重新创建消费者（先移除再创建）
        /// </summary>
        public async Task<IMessageConsumer> RecreateConsumerAsync(ConsumerOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            // 先移除现有的消费者（如果存在）
            if (_consumers.ContainsKey(options.Name))
            {
                Log.Information("强制重新创建消费者，先移除现有消费者: {Name}", options.Name);
                await RemoveConsumerAsync(options.Name, cancellationToken);

                // 等待一小段时间确保清理完成
                await Task.Delay(100, cancellationToken);
            }

            // 清理可能残留的 ResiliencePipeline
            var pipelineKey = $"$Consumer_{options.ConsumerId}";
            if (!string.IsNullOrWhiteSpace(pipelineKey))
            {
                ResiliencePipelineManager.TryRemove(pipelineKey);
                Log.Debug("已清理残留的 ResiliencePipeline: {PipelineKey}", pipelineKey);
            }

            // 创建新的消费者
            return await AddConsumerAsync(options, cancellationToken);
        }

        public async Task StartAllAsync(CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            // 并发启动所有消息总线
            var busStartTasks = _messageBuses.Values
                .Select(async messageBus =>
                {
                    try
                    {
                        await messageBus.ConnectAsync(cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "启动消息总线时发生错误");
                        throw;
                    }
                });

            await Task.WhenAll(busStartTasks);

            // 并发启动所有启用的消费者
            var consumerStartTasks = _consumers.Values
                .Where(c => c.Options.IsEnabled)
                .Select(async consumer =>
                {
                    try
                    {
                        await consumer.StartAsync(cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "启动消费者时发生错误: {Name}", consumer.Name);
                        throw;
                    }
                });

            await Task.WhenAll(consumerStartTasks);
        }

        public async Task StopAllAsync(CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();

            // 并发停止所有消费者
            var consumerStopTasks = _consumers.Values
                .Select(async consumer =>
                {
                    try
                    {
                        await consumer.StopAsync(cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "停止消费者时发生错误: {Name}", consumer.Name);
                    }
                });

            await Task.WhenAll(consumerStopTasks);

            // 并发断开所有消息总线
            var busStopTasks = _messageBuses.Values
                .Select(async messageBus =>
                {
                    try
                    {
                        await messageBus.DisconnectAsync(cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "断开消息总线连接时发生错误");
                    }
                });

            await Task.WhenAll(busStopTasks);
        }

        private void ThrowIfDisposed()
        {
            if (_isDisposed)
            {
                throw new ObjectDisposedException(GetType().Name);
            }
        }

        public async ValueTask DisposeAsync()
        {
            if (_isDisposed) return;

            // 使用原子操作确保只执行一次
            var wasDisposed = _isDisposed;
            _isDisposed = true;
            if (wasDisposed)
            {
                return; // 已经被其他线程处理了
            }

            try
            {
                // 并发停止所有消费者
                var consumerStopTasks = _consumers.Values
                    .Select(async consumer =>
                    {
                        try
                        {
                            await consumer.StopAsync(CancellationToken.None);
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, "释放过程中停止消费者时发生错误: {Name}", consumer.Name);
                        }
                    });

                await Task.WhenAll(consumerStopTasks);

                // 并发断开所有消息总线
                var busStopTasks = _messageBuses.Values
                    .Select(async messageBus =>
                    {
                        try
                        {
                            await messageBus.DisconnectAsync(CancellationToken.None);
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, "释放过程中断开消息总线连接时发生错误");
                        }
                    });

                await Task.WhenAll(busStopTasks);

                // 并发释放所有消费者
                var consumerDisposeTasks = _consumers.Values
                    .Select(async consumer =>
                    {
                        try
                        {
                            await consumer.DisposeAsync();
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, "释放消费者时发生错误: {Name}", consumer.Name);
                        }
                    });

                await Task.WhenAll(consumerDisposeTasks);
                _consumers.Clear();

                // 并发释放所有消息总线
                var busDisposeTasks = _messageBuses.Values
                    .Select(async messageBus =>
                    {
                        try
                        {
                            await messageBus.DisposeAsync();
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, "释放消息总线时发生错误");
                        }
                    });

                await Task.WhenAll(busDisposeTasks);
                _messageBuses.Clear();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "释放资源过程中发生错误");
            }
        }
    }
} 