// ---------------------------------------------------------------------------------------------------
// <auto-generated>
// This code was generated by LinqToDB scaffolding tool (https://github.com/linq2db/linq2db).
// Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
// ---------------------------------------------------------------------------------------------------

using LinqToDB.Mapping;
using System;

#pragma warning disable 1573, 1591
#nullable enable

namespace GCP.DataAccess
{
	/// <summary>
	/// 动作库执行日志
	/// </summary>
	[Table("lc_action_library_log")]
	public class LcActionLibraryLog
	{
		/// <summary>
		/// Description:数据行 ID 号
		/// </summary>
		[Column("ID"               , CanBeNull = false, IsPrimaryKey = true)] public string    Id              { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:动作库ID
		/// </summary>
		[Column("ACTION_LIBRARY_ID", CanBeNull = false                     )] public string    ActionLibraryId { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:执行实例ID
		/// </summary>
		[Column("EXECUTION_ID"     , CanBeNull = false                     )] public string    ExecutionId     { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:关联的流程实例ID
		/// </summary>
		[Column("FLOW_PROC_ID"                                             )] public string?   FlowProcId      { get; set; } // varchar(36)
		/// <summary>
		/// Description:关联的流程步骤ID
		/// </summary>
		[Column("FLOW_STEP_ID"                                             )] public string?   FlowStepId      { get; set; } // varchar(36)
		/// <summary>
		/// Description:输入数据JSON
		/// </summary>
		[Column("INPUT_DATA"                                               )] public string?   InputData       { get; set; } // longtext
		/// <summary>
		/// Description:输出数据JSON
		/// </summary>
		[Column("OUTPUT_DATA"                                              )] public string?   OutputData      { get; set; } // longtext
		/// <summary>
		/// Description:错误信息
		/// </summary>
		[Column("ERROR_MESSAGE"                                            )] public string?   ErrorMessage    { get; set; } // longtext
		/// <summary>
		/// Description:堆栈跟踪
		/// </summary>
		[Column("STACK_TRACE"                                              )] public string?   StackTrace      { get; set; } // longtext
		/// <summary>
		/// Description:执行状态 success、error、timeout
		/// </summary>
		[Column("STATUS"           , CanBeNull = false                     )] public string    Status          { get; set; } = null!; // varchar(20)
		/// <summary>
		/// Description:执行时间(毫秒)
		/// </summary>
		[Column("EXECUTION_TIME_MS"                                        )] public int?      ExecutionTimeMs { get; set; } // int
		/// <summary>
		/// Description:开始时间
		/// </summary>
		[Column("START_TIME"                                               )] public DateTime  StartTime       { get; set; } // datetime
		/// <summary>
		/// Description:结束时间
		/// </summary>
		[Column("END_TIME"                                                 )] public DateTime? EndTime         { get; set; } // datetime
		/// <summary>
		/// Description:解决方案 ID
		/// </summary>
		[Column("SOLUTION_ID"      , CanBeNull = false                     )] public string    SolutionId      { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:项目 ID
		/// </summary>
		[Column("PROJECT_ID"       , CanBeNull = false                     )] public string    ProjectId       { get; set; } = null!; // varchar(36)
		/// <summary>
		/// Description:执行者
		/// </summary>
		[Column("CREATOR"          , CanBeNull = false                     )] public string    Creator         { get; set; } = null!; // varchar(80)
	}
}
