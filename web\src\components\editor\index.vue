<template>
  <vue-monaco-editor
    class="editor"
    :language="props.language"
    theme="vs-dark"
    :options="MONACO_EDITOR_OPTIONS"
    :value="props.value"
    @mount="handleMount"
  />
</template>
<script lang="ts">
export default {
  name: 'Editor',
};
</script>
<script setup lang="ts">
import { VueMonacoEditor } from '@guolao/vue-monaco-editor';
import { editor } from 'monaco-editor';
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api';
import { computed, shallowRef, watch } from 'vue';

// 导入智能提示功能
import { updateIntelliSenseData } from './scriptCompletion';
import './loader'; // 确保加载智能提示配置

interface VariableData {
  id: string;
  key: string;
  path?: string;
  pathDescription?: string;
  description?: string;
  type: string;
  children?: VariableData[];
}

interface FunctionData {
  value: string;
  label: string;
  script: string;
  remark: string;
}

const props = withDefaults(
  defineProps<{
    value?: string;
    language: string;
    readOnly?: boolean;
    autoWrap?: boolean;
    showLineNumbers?: boolean;
    enableIntellisense?: boolean;
    currentVariables?: VariableData[];
    localVariables?: VariableData[];
    globalVariables?: VariableData[];
    functions?: FunctionData[];
  }>(),
  {
    language: 'javascript',
    readOnly: false,
    autoWrap: true,
    showLineNumbers: true,
    enableIntellisense: false,
    currentVariables: () => [],
    localVariables: () => [],
    globalVariables: () => [],
    functions: () => [],
  },
);

const emit = defineEmits<{
  'update:value': [value: string];
}>();

// 监听智能提示相关属性的变化，更新智能提示数据
watch(
  [
    () => props.currentVariables,
    () => props.localVariables,
    () => props.globalVariables,
    () => props.functions,
    () => props.enableIntellisense,
  ],
  () => {
    if (props.enableIntellisense) {
      updateIntelliSenseData({
        currentVariables: props.currentVariables,
        localVariables: props.localVariables,
        globalVariables: props.globalVariables,
        functions: props.functions,
      });
    }
  },
  { immediate: true, deep: true },
);

const MONACO_EDITOR_OPTIONS = computed<editor.IStandaloneEditorConstructionOptions>(() => {
  const options: editor.IStandaloneEditorConstructionOptions = {
    automaticLayout: true,
    readOnly: props.readOnly,
    formatOnType: true,
    formatOnPaste: true,
    lineNumbers: props.showLineNumbers ? 'on' : 'off',
    // 启用智能提示相关选项
    suggestOnTriggerCharacters: true,
    quickSuggestions: true,
    wordBasedSuggestions: 'off', // 关闭基于单词的建议，使用自定义建议
    acceptSuggestionOnEnter: 'on',
    acceptSuggestionOnCommitCharacter: true,
    // 设置智能提示触发字符
    suggest: {
      showKeywords: true,
      showSnippets: true,
      showFunctions: true,
      showVariables: true,
      showModules: true,
      showProperties: true,
      showMethods: true,
      showClasses: true,
      showInterfaces: true,
      showEnums: true,
      showValues: true,
      showConstants: true,
      showEvents: true,
      showOperators: true,
      showTypeParameters: true,
      showReferences: true,
      showFolders: true,
      showFiles: true,
      showColors: true,
      showUnits: true,
      showStructs: true,
      showFields: true,
      showConstructors: true,
      showUsers: true,
      showIssues: true,
    },
  };
  if (props.autoWrap) {
    options.wordWrap = 'on';
    options.minimap = {
      enabled: false,
    };
  }
  return options;
});

const editorRef = shallowRef<Partial<monaco.editor.IStandaloneCodeEditor>>();
const handleMount = (editor: any) => {
  editorRef.value = editor;

  // 监听内容变化，发出 update:value 事件
  editor.onDidChangeModelContent(() => {
    const value = editor.getValue();
    emit('update:value', value);
  });

  // 设置初始值
  if (props.value !== undefined) {
    editor.setValue(props.value);
  }

  // 确保智能提示数据已更新
  if (props.enableIntellisense) {
    updateIntelliSenseData({
      currentVariables: props.currentVariables,
      localVariables: props.localVariables,
      globalVariables: props.globalVariables,
      functions: props.functions,
    });

    // 手动触发智能提示（用于测试）
    setTimeout(() => {
      console.log('编辑器已挂载，智能提示已配置');
      console.log('可以按 Ctrl+Space 手动触发智能提示');
    }, 100);
  }
};

// 监听 value 属性变化，更新编辑器内容
watch(
  () => props.value,
  (newValue) => {
    if (editorRef.value && newValue !== editorRef.value.getValue()) {
      editorRef.value.setValue(newValue || '');
    }
  },
);

const formatCode = () => {
  editorRef.value?.getAction('editor.action.formatDocument').run();
};

const insertText = (text) => {
  const selection = editorRef.value.getSelection();
  const range = new monaco.Range(
    selection.startLineNumber,
    selection.startColumn,
    selection.endLineNumber,
    selection.endColumn,
  );

  editorRef.value.executeEdits('insertText', [
    {
      range,
      text,
      forceMoveMarkers: true,
    },
  ]);

  editorRef.value.focus();
};
defineExpose({
  formatCode,
  insertText,
  editorRef,
});
</script>
<style lang="less" scoped>
.editor {
  border: 1px solid var(--td-border-level-2-color);
  border-radius: var(--td-radius-default);
}
</style>
