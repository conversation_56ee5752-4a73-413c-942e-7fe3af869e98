<template>
  <div class="dingtalk-config">
    <!-- 通知类型选择 -->
    <t-form-item label="通知类型" name="notificationType">
      <t-radio-group v-model="config.notificationType" @change="handleNotificationTypeChange">
        <t-radio value="robot">群机器人</t-radio>
        <t-radio value="workNotice">工作通知</t-radio>
      </t-radio-group>
    </t-form-item>

    <!-- 群机器人配置 -->
    <template v-if="config.notificationType === 'robot'">
      <t-form-item label="Webhook地址" name="webHook">
        <t-input v-model="config.webHook" placeholder="请输入钉钉机器人Webhook地址" />
      </t-form-item>

      <t-form-item label="签名密钥" name="secret">
        <t-input v-model="config.secret" placeholder="请输入签名密钥（可选）" />
      </t-form-item>

      <t-form-item label="@所有人" name="isAtAll">
        <t-switch v-model="config.isAtAll" />
      </t-form-item>

      <t-form-item label="@指定人员" name="atMobiles">
        <div class="mobile-list">
          <div v-for="(mobile, index) in config.atMobiles" :key="index" class="mobile-item">
            <t-input v-model="config.atMobiles[index]" placeholder="请输入手机号" />
            <t-button theme="danger" variant="text" @click="removeMobile(index)"> 删除 </t-button>
          </div>
          <t-button theme="primary" variant="text" @click="addMobile"> 添加手机号 </t-button>
        </div>
      </t-form-item>
    </template>

    <!-- 工作通知配置 -->
    <template v-if="config.notificationType === 'workNotice'">
      <t-form-item label="AppKey" name="appKey">
        <t-input v-model="config.appKey" placeholder="请输入企业内部应用的AppKey" />
      </t-form-item>

      <t-form-item label="AppSecret" name="appSecret">
        <t-input v-model="config.appSecret" type="password" placeholder="请输入企业内部应用的AppSecret" />
      </t-form-item>

      <t-form-item label="AgentId" name="agentId">
        <t-input-number v-model="config.agentId" placeholder="请输入应用的AgentId" />
      </t-form-item>

      <t-form-item label="接收人员" name="userIds">
        <div class="user-list">
          <div v-for="(userId, index) in config.userIds" :key="index" class="user-item">
            <t-input v-model="config.userIds[index]" placeholder="请输入用户ID" />
            <t-button theme="danger" variant="text" @click="removeUser(index)"> 删除 </t-button>
          </div>
          <t-button theme="primary" variant="text" @click="addUser"> 添加用户 </t-button>
        </div>
      </t-form-item>

      <t-form-item label="接收部门" name="deptIds">
        <div class="dept-list">
          <div v-for="(deptId, index) in config.deptIds" :key="index" class="dept-item">
            <t-input-number v-model="config.deptIds[index]" placeholder="请输入部门ID" />
            <t-button theme="danger" variant="text" @click="removeDept(index)"> 删除 </t-button>
          </div>
          <t-button theme="primary" variant="text" @click="addDept"> 添加部门 </t-button>
        </div>
      </t-form-item>

      <t-form-item label="发送给全员" name="toAllUser">
        <t-switch v-model="config.toAllUser" />
      </t-form-item>
    </template>

    <!-- 配置说明 -->
    <t-alert theme="info" title="配置说明">
      <template v-if="config.notificationType === 'robot'">
        <p>1. Webhook地址：在钉钉群中添加自定义机器人后获得</p>
        <p>2. 签名密钥：如果机器人启用了加签验证，需要填写此项</p>
        <p>3. @功能：可以选择@所有人或@指定手机号的人员</p>
      </template>
      <template v-else-if="config.notificationType === 'workNotice'">
        <p>1. AppKey/AppSecret：企业内部应用的凭证，在钉钉开发者后台获取</p>
        <p>2. AgentId：应用的唯一标识，在应用详情页面查看</p>
        <p>3. 接收人员：指定接收通知的用户ID列表</p>
        <p>4. 接收部门：指定接收通知的部门ID列表</p>
        <p>5. 发送给全员：开启后将发送给企业所有成员</p>
      </template>
    </t-alert>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// Props
const props = defineProps<{
  modelValue: {
    notificationType: string;
    // 群机器人配置
    webHook: string;
    secret: string;
    isAtAll: boolean;
    atMobiles: string[];
    // 工作通知配置
    appKey: string;
    appSecret: string;
    agentId: number;
    userIds: string[];
    deptIds: number[];
    toAllUser: boolean;
  };
}>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any];
}>();

// 计算属性
const config = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

// 方法
const handleNotificationTypeChange = () => {
  // 切换通知类型时重置相关配置
  if (config.value.notificationType === 'robot') {
    // 重置工作通知配置
    config.value.appKey = '';
    config.value.appSecret = '';
    config.value.agentId = 0;
    config.value.userIds = [];
    config.value.deptIds = [];
    config.value.toAllUser = false;
  } else if (config.value.notificationType === 'workNotice') {
    // 重置群机器人配置
    config.value.webHook = '';
    config.value.secret = '';
    config.value.isAtAll = false;
    config.value.atMobiles = [];
  }
};

const addMobile = () => {
  config.value.atMobiles.push('');
};

const removeMobile = (index: number) => {
  config.value.atMobiles.splice(index, 1);
};

const addUser = () => {
  config.value.userIds.push('');
};

const removeUser = (index: number) => {
  config.value.userIds.splice(index, 1);
};

const addDept = () => {
  config.value.deptIds.push(0);
};

const removeDept = (index: number) => {
  config.value.deptIds.splice(index, 1);
};
</script>

<style scoped>
.dingtalk-config {
  padding: 10px 0;
}

.mobile-list,
.user-list,
.dept-list {
  width: 100%;
}

.mobile-item,
.user-item,
.dept-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.mobile-item .t-input,
.user-item .t-input,
.dept-item .t-input-number {
  flex: 1;
}

.t-alert {
  margin-top: 20px;
}
</style>
