<template>
  <div class="variable-tree">
    <t-input readonly size="small" :value="activePath" placeholder="选中变量路径" class="path-input" />
    <div class="tree-container">
      <t-tree
        ref="treeRef"
        v-model:actived="activeValue"
        :data="data"
        :expand-level="1"
        activable
        transition
        hover
        line
        :filter="filter"
        @click="onClick"
        @dblclick="onDblClick"
      >
        <template #label="{ node }">
          <div class="tree-node-content">
            <t-space size="small" class="node-info">
              <div style="font-size: 12px">{{ node.label }}</div>
              <t-tag v-if="node.data.data.type" size="small" theme="success" variant="outline">{{
                VALUE_TYPE_MAP[node.data.data.type] || node.data.data.type
              }}</t-tag>
              <t-tag v-if="node.data.data.description" size="small" theme="primary" variant="outline">{{
                node.data.data.description
              }}</t-tag>
            </t-space>
            <!-- 快速编辑和删除按钮 -->
            <div v-if="props.editable" class="node-actions">
              <t-button size="small" variant="text" @click.stop="onEdit(node.data.data)" title="编辑">
                <template #icon>
                  <edit-icon />
                </template>
              </t-button>
              <t-button size="small" variant="text" theme="danger" @click.stop="onDelete(node.data.data)" title="删除">
                <template #icon>
                  <delete-icon />
                </template>
              </t-button>
            </div>
          </div>
        </template>
      </t-tree>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'VariableTree',
};
</script>
<script setup lang="ts">
import { cloneDeep, debounce } from 'lodash-es';
import { MessagePlugin, TreeInstanceFunctions, TreeProps } from 'tdesign-vue-next';
import { computed, ref, watch, watchEffect } from 'vue';
import { EditIcon, DeleteIcon } from 'tdesign-icons-vue-next';

import { VALUE_TYPE_MAP } from './constants';
import { FlowData } from './model';
import { processVariableListForTree, TreeData } from './utils/variableProcessor';

const props = defineProps<{
  activeId?: string;
  filterText?: string;
  limitTypes?: string[];
  editable?: boolean;
  variableList: FlowData[];
}>();

const emits = defineEmits(['click', 'dblclick', 'update:activeId', 'edit', 'delete']);
const data = computed<TreeData[]>(() => {
  // 使用封装的变量处理逻辑
  const result = processVariableListForTree(cloneDeep(props.variableList || []));
  return result;
});

const treeRef = ref<TreeInstanceFunctions>();
const filter = ref<TreeProps['filter']>();
watch(
  () => props.filterText,
  debounce((val) => {
    if (val) {
      const lowerCaseVal = val.toLowerCase();
      // const hasLimitTypes = props.limitTypes && props.limitTypes.length > 0;
      filter.value = (node) => {
        const data = node.data.data as FlowData;
        // if (hasLimitTypes && !props.limitTypes.includes(data.type)) return false;
        const rs =
          (data.key as string).toLowerCase().indexOf(lowerCaseVal) >= 0 ||
          (data.pathDescription as string).toLowerCase().indexOf(lowerCaseVal) >= 0;
        return rs;
      };
      return;
    }
    filter.value = undefined;
  }, 300),
  { immediate: true },
);

const activeValue = ref([]);
const activePath = ref();
const activeItem = ref();

watchEffect(() => {
  activeValue.value = props.activeId ? [props.activeId] : [];
  activePath.value = props.activeId;
});

watch(activePath, (newVal, oldVal) => {
  if (newVal !== oldVal) return;
  activeValue.value = newVal ? [newVal] : [];
});

const onClick = (context: any) => {
  const { node } = context;
  activeItem.value = node.data;
  activePath.value = node.value;
  emits('click', {
    path: activePath.value,
    item: activeItem.value,
  });
};

const onDblClick = () => {
  if (props.limitTypes && props.limitTypes.length > 0 && !props.limitTypes.includes(activeItem.value.data.type)) {
    MessagePlugin.error(`请选择【${props.limitTypes.map((t) => VALUE_TYPE_MAP[t]).join('、')}】类型变量`);
    return;
  }
  emits('update:activeId', activePath.value);
  emits('dblclick', {
    path: activePath.value,
    item: activeItem.value,
  });
};

// 编辑变量
const onEdit = (data: FlowData) => {
  emits('edit', data);
};

// 删除变量
const onDelete = (data: FlowData) => {
  emits('delete', data);
};

const scrollToActive = debounce(() => {
  if (treeRef.value && props.activeId) {
    treeRef.value.scrollTo({
      key: props.activeId,
    });
  }
}, 300);

defineExpose({
  scrollToActive,
});
</script>
<style lang="less" scoped>
.variable-tree {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .path-input {
    flex-shrink: 0;
    margin-bottom: 8px;
  }

  .tree-container {
    flex: 1;
    overflow: hidden;
    min-height: 0; /* 确保flex子项可以缩小 */

    :deep(.t-tree) {
      height: 100%;
      overflow-y: auto;
    }

    /* 确保树的内容区域正确显示 */
    :deep(.t-tree__list) {
      height: auto !important;
      min-height: 100%;
    }
  }
}

/* 树节点内容样式 */
.tree-node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .node-info {
    flex: 1;
    min-width: 0; /* 允许内容收缩 */
  }

  .node-actions {
    display: flex;
    gap: 2px;
    opacity: 0;
    transition: opacity 0.2s;
    margin-left: 8px;
    flex-shrink: 0;
  }

  &:hover .node-actions {
    opacity: 1;
  }
}
</style>
