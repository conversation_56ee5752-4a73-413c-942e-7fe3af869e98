﻿using GCP.Common;
using GCP.DataAccess;
using LinqToDB;

namespace GCP.Functions.Common.Services
{
    [Function("user", "用户服务")]
    internal class UserService : BaseService
    {
        [Function("getAll", "获取用户清单")]
        public List<LcUser> GetAll()
        {
            using var db = this.GetDb();
            var data = (from a in db.LcUsers
                        where a.State == 1
                        select a).ToList();
            return data;
        }

        [Function("currentInfo", "获取当前用户信息")]
        public dynamic CurrentInfo()
        {
            if (string.IsNullOrEmpty(this.UserName))
            {
                throw new CustomException("请先登录");
            }


            using var db = this.GetDb();
            var user = db.LcUsers.FirstOrDefault(a => a.UserName == this.UserName);
            if (user == null)
            {
                throw new CustomException("用户不存在");
            }

            var roles = new List<string>();
            var isAdmin = this.UserName == "admin";
            if (isAdmin)
            {
                roles.Add("all");
            }
            else
            {
                // 查询数据库获取角色
            }

            return new
            {
                name = user.DisplayName,
                roles
            };
        }

        [Function("add", "新增用户")]
        public bool Add(LcUser user)
        {
            using var db = this.GetDb();
            var data = db.LcUsers.FirstOrDefault(a =>
                a.UserName == user.UserName);

            if (data != null)
            {
                if (data.State == 0)
                {
                    db.LcUsers.Delete(a => a.Id == data.Id);
                }
                else
                {
                    throw new CustomException("用户已存在，请勿重复");
                }
            }

            this.InsertData(user);
            return true;
        }

        [Function("update", "编辑用户")]
        public bool Update(LcUser user)
        {
            using var db = this.GetDb();
            var data = db.LcUsers.FirstOrDefault(a => a.Id == user.Id);
            if (data == null)
            {
                return false;
            }
            data.UserName = user.UserName;
            data.Phone = user.Phone;
            data.Email = user.Email;
            this.UpdateData(data, db);
            return true;
        }

        [Function("delete", "删除用户")]
        public bool Delete(string userId)
        {
            using var db = this.GetDb();
            var data = db.LcUsers.FirstOrDefault(a => a.Id == userId);
            if (data == null)
            {
                return false;
            }
            data.State = 0;
            this.UpdateData(data, db);
            return true;
        }

        [Function("login", "系统用户登录")]
        public string Login(string username, string password)
        {
            using var db = this.GetDb();
            var data = db.LcUsers.FirstOrDefault(a => a.UserName == username);
            if (data != null && data.Password == password && username == data.Password)
            {
                data.Password = CryptoHelper.PasswordEncrypt(password);
                db.LcUsers
                    .Where(t => t.Id == data.Id)
                    .Set(t => t.Password, data.Password)
                    .Update();
            }

            if (data == null || !CryptoHelper.PasswordVerify(password, data.Password))
            {
                if (data == null && username == "admin")
                {
                    var count = db.LcUsers.Count();
                    if (count == 0)
                    {
                        this.userName = "sys";

                        this.InsertData(new LcUser()
                        {
                            UserName = "admin",
                            DisplayName = "管理员",
                            Password = CryptoHelper.PasswordEncrypt("admin"),
                        }, db);
                    }
                }
                throw new CustomException("用户名或密码错误");
            }

            if (data.State == 0)
            {
                throw new CustomException("用户已被禁用");
            }

            var token = Guid.NewGuid().ToString();

            this.Cache.Set("token_" + token, data.UserName, TimeSpan.FromHours(8));

            return token;
        }

        [Function("logout", "系统用户登出")]
        public bool Logout(string token)
        {
            this.Cache.Remove("token_" + token);
            return true;
        }

        [Function("updatePassword", "修改密码")]
        public bool UpdatePassword(string userId, string oldPassword, string newPassword)
        {
            using var db = this.GetDb();
            var data = db.LcUsers.FirstOrDefault(a => a.Id == userId);
            if (data == null || !CryptoHelper.PasswordVerify(oldPassword, data.Password))
            {
                throw new CustomException("用户名或密码错误");
            }
            data.Password = CryptoHelper.PasswordEncrypt(newPassword);
            this.UpdateData(data, db);
            return true;
        }
    }
}
