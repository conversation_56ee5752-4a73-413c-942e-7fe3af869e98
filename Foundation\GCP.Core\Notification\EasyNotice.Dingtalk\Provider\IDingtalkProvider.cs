﻿using EasyNotice.Core;
using EasyNotice.Dingtalk;

namespace EasyNotice
{
    public interface IDingtalkProvider : IEasyNotice
    {
        Task<EasyNoticeSendResponse> SendTextAsync(string text, EasyNoticeAtUser atUser = null);

        Task<EasyNoticeSendResponse> SendTextAsync(TextMessage message);

        Task<EasyNoticeSendResponse> SendMarkdownAsync(string title, string text, EasyNoticeAtUser atUser = null);

        Task<EasyNoticeSendResponse> SendMarkdownAsync(MarkdownMessage message);

        Task<EasyNoticeSendResponse> SendActionCardAsync(string title, string text, string singleTitle, string singleURL, string btnOrientation = "0");

        Task<EasyNoticeSendResponse> SendActionCardAsync(ActionCardMessage message);

        /// <summary>
        /// 发送工作通知
        /// </summary>
        Task<EasyNoticeSendResponse> SendWorkNoticeAsync(string title, string content);

        /// <summary>
        /// 发送工作通知（指定接收人员）
        /// </summary>
        Task<EasyNoticeSendResponse> SendWorkNoticeAsync(string title, string content, List<string> userIds, List<string> deptIds = null, bool toAllUser = false);
    }
}
