using GCP.Common;
using GCP.DataAccess;
using System.Diagnostics;
using Jint;
using LinqToDB;

namespace GCP.Functions.Common.Services
{
    /// <summary>
    /// 在线函数管理服务
    /// </summary>
    [Function("onlineFunction", "在线函数管理服务")]
    internal class OnlineFunctionService : BaseService
    {
        /// <summary>
        /// 获取在线函数列表
        /// </summary>
        /// <param name="keyword">搜索关键词</param>
        /// <param name="functionType">函数类型</param>
        /// <param name="status">函数状态</param>
        /// <returns>函数列表</returns>
        [Function("getFunctions", "获取在线函数列表")]
        public async Task<List<object>> GetFunctionsAsync(string keyword = null, string functionType = null, string status = null)
        {
            try
            {
                using var db = this.GetDb();
                
                var query = db.LcOnlineFunctions
                    .Where(x => x.State == 1 && x.SolutionId == this.SolutionId && x.ProjectId == this.ProjectId);
                
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(x => x.Name.Contains(keyword) || 
                                           (x.Description != null && x.Description.Contains(keyword)));
                }
                
                if (!string.IsNullOrEmpty(functionType))
                {
                    query = query.Where(x => x.FunctionType == functionType);
                }
                
                if (!string.IsNullOrEmpty(status))
                {
                    query = query.Where(x => x.Status == status);
                }
                
                var functions = await query.ToListAsync();
                
                var result = functions.Select(func => (object)new
                {
                    func.Id,
                    func.Name,
                    func.FunctionType,
                    func.Description,
                    func.Status,
                    func.Version,
                    // 从JSON字段解析参数和输出信息
                    InputParameters = ParseInputParameters(func.InputParametersJson),
                    OutputFormat = ParseOutputFormat(func.OutputFormatJson)
                }).ToList();
                
                return result;
            }
            catch (Exception ex)
            {
                throw new CustomException($"获取在线函数列表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取在线函数详情
        /// </summary>
        /// <param name="id">函数ID</param>
        /// <returns>函数详情</returns>
        [Function("getFunction", "获取在线函数详情")]
        public async Task<object> GetFunctionAsync(string id)
        {
            try
            {
                using var db = this.GetDb();
                
                var function = await db.LcOnlineFunctions
                    .FirstOrDefaultAsync(x => x.Id == id && x.State == 1 && 
                                           x.SolutionId == this.SolutionId && x.ProjectId == this.ProjectId);
                
                if (function == null)
                {
                    throw new CustomException("函数不存在或无权限访问");
                }
                
                return new
                {
                    function.Id,
                    function.Name,
                    function.FunctionType,
                    function.Description,
                    function.Code,
                    function.Status,
                    function.Version,
                    // 从JSON字段解析参数和输出信息
                    InputParameters = ParseInputParameters(function.InputParametersJson),
                    OutputFormat = ParseOutputFormat(function.OutputFormatJson)
                };
            }
            catch (Exception ex)
            {
                throw new CustomException($"获取函数详情失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 保存在线函数
        /// </summary>
        /// <param name="functionData">函数数据</param>
        /// <returns>保存结果</returns>
        [Function("saveFunction", "保存在线函数")]
        public async Task<object> SaveFunctionAsync(object functionDataObj)
        {
            try
            {
                // 将object转换为dynamic以便访问属性
                dynamic functionData = functionDataObj;

                using var db = this.GetDb();
                using var transaction = await db.BeginTransactionAsync();

                try
                {
                    // 提取所有dynamic属性到变量中，避免在LINQ表达式中使用dynamic
                    var idValue = functionData.id?.ToString();
                    var nameValue = functionData.name?.ToString();
                    var functionTypeValue = functionData.functionType?.ToString();
                    var descriptionValue = functionData.description?.ToString();
                    var codeValue = functionData.code?.ToString();
                    var statusValue = functionData.status?.ToString() ?? "active";

                    var isUpdate = !string.IsNullOrEmpty(idValue);
                    var functionId = isUpdate ? idValue! : TUID.NewTUID().ToString();

                    // 获取当前解决方案和项目ID
                    var currentSolutionId = this.SolutionId?.ToString();
                    var currentProjectId = this.ProjectId?.ToString();

                    LcOnlineFunction function;

                    if (isUpdate)
                    {
                        // 使用ToList()避免dynamic操作问题
                        var functions = await db.LcOnlineFunctions
                            .Where(x => x.State == 1)
                            .ToListAsync();

                        function = functions.FirstOrDefault(x =>
                            x.Id == functionId &&
                            x.SolutionId == currentSolutionId &&
                            x.ProjectId == currentProjectId);
                        
                        if (function == null)
                        {
                            throw new CustomException("函数不存在或无权限修改");
                        }
                        
                        function.Name = nameValue;
                        function.FunctionType = functionTypeValue;
                        function.Description = descriptionValue;
                        function.Code = codeValue;
                        function.Status = statusValue;
                        function.Version += 1; // 版本号递增
                        function.Modifier = this.Context.clientID;
                        function.TimeModified = DateTime.Now;

                        // 更新参数和输出格式JSON
                        function.InputParametersJson = functionData.inputParameters != null ?
                            JsonHelper.Serialize(functionData.inputParameters) : null;
                        function.OutputFormatJson = functionData.outputFormat != null ?
                            JsonHelper.Serialize(functionData.outputFormat) : null;
                        
                        db.Update(function);
                    }
                    else
                    {
                        function = new LcOnlineFunction
                        {
                            Id = functionId,
                            Name = nameValue,
                            FunctionType = functionTypeValue,
                            Description = descriptionValue,
                            Code = codeValue,
                            Status = statusValue,
                            Version = 1,
                            SolutionId = this.SolutionId,
                            ProjectId = this.ProjectId,
                            Creator = this.Context.clientID,
                            TimeCreate = DateTime.Now,
                            State = 1,
                            // 设置参数和输出格式JSON
                            InputParametersJson = functionData.inputParameters != null ?
                                JsonHelper.Serialize(functionData.inputParameters) : null,
                            OutputFormatJson = functionData.outputFormat != null ?
                                JsonHelper.Serialize(functionData.outputFormat) : null
                        };

                        db.Insert(function);
                    }
                    
                    // 参数和输出格式已经保存在JSON字段中，无需单独处理


                    
                    // LinqToDB不需要SaveChangesAsync，Insert/Update操作会立即执行
                    await transaction.CommitAsync();
                    
                    return new { Success = true, Message = isUpdate ? "函数更新成功" : "函数创建成功", Id = functionId };
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                throw new CustomException($"保存函数失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 删除在线函数
        /// </summary>
        /// <param name="id">函数ID</param>
        /// <returns>删除结果</returns>
        [Function("deleteFunction", "删除在线函数")]
        public async Task<object> DeleteFunctionAsync(string id)
        {
            try
            {
                using var db = this.GetDb();
                
                var function = await db.LcOnlineFunctions
                    .FirstOrDefaultAsync(x => x.Id == id && x.State == 1 && 
                                           x.SolutionId == this.SolutionId && x.ProjectId == this.ProjectId);
                
                if (function == null)
                {
                    throw new CustomException("函数不存在或无权限删除");
                }
                
                // 软删除函数
                function.State = 0;
                function.Modifier = this.Context.clientID;
                function.TimeModified = DateTime.Now;
                
                // 参数和输出格式已经在JSON字段中，无需单独删除
                
                db.Update(function);
                // LinqToDB不需要SaveChangesAsync，Update操作会立即执行
                
                return new { Success = true, Message = "函数删除成功" };
            }
            catch (Exception ex)
            {
                throw new CustomException($"删除函数失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 测试在线函数
        /// </summary>
        /// <param name="id">函数ID</param>
        /// <param name="parameters">测试参数</param>
        /// <returns>测试结果</returns>
        [Function("testFunction", "测试在线函数")]
        public async Task<object> TestFunctionAsync(string id, Dictionary<string, object> parameters = null)
        {
            var executionId = TUID.NewTUID().ToString();
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                using var db = this.GetDb();
                
                var function = await db.LcOnlineFunctions
                    .FirstOrDefaultAsync(x => x.Id == id && x.State == 1 && 
                                           x.SolutionId == this.SolutionId && x.ProjectId == this.ProjectId);
                
                if (function == null)
                {
                    throw new CustomException("函数不存在或无权限访问");
                }
                
                object result;
                
                if (function.FunctionType == "javascript")
                {
                    result = await ExecuteJavaScriptFunction(function.Code, parameters ?? new Dictionary<string, object>());
                }
                else if (function.FunctionType == "csharp")
                {
                    throw new CustomException("C#函数执行功能待实现");
                }
                else
                {
                    throw new CustomException($"不支持的函数类型: {function.FunctionType}");
                }
                
                stopwatch.Stop();
                
                
                return new
                {
                    Success = true,
                    Result = result,
                    ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                    ExecutionId = executionId
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                
                return new
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    ExecutionTimeMs = stopwatch.ElapsedMilliseconds,
                    ExecutionId = executionId
                };
            }
        }

        #region 私有方法

        /// <summary>
        /// 执行JavaScript函数
        /// </summary>
        private async Task<object> ExecuteJavaScriptFunction(string code, Dictionary<string, object> parameters)
        {
            await Task.CompletedTask;
            
            var engine = new Engine();
            
            // 注入参数
            foreach (var param in parameters)
            {
                engine.SetValue(param.Key, param.Value);
            }
            
            // 执行代码
            var result = engine.Evaluate(code);
            
            return result.GetObject();
        }

        /// <summary>
        /// 解析输入参数JSON
        /// </summary>
        /// <param name="inputParametersJson">输入参数JSON字符串</param>
        /// <returns>参数列表</returns>
        private List<object> ParseInputParameters(string inputParametersJson)
        {
            if (string.IsNullOrEmpty(inputParametersJson))
            {
                return new List<object>();
            }

            try
            {
                var parameters = JsonHelper.Deserialize<List<dynamic>>(inputParametersJson);
                return parameters.Select((param, index) => new
                {
                    ParameterName = param.name?.ToString(),
                    ParameterType = param.type?.ToString(),
                    IsRequired = param.required ?? true,
                    DefaultValue = param.defaultValue?.ToString(),
                    Description = param.description?.ToString(),
                    OrderIndex = index
                }).Cast<object>().ToList();
            }
            catch
            {
                return new List<object>();
            }
        }

        /// <summary>
        /// 解析输出格式JSON
        /// </summary>
        /// <param name="outputFormatJson">输出格式JSON字符串</param>
        /// <returns>输出格式对象</returns>
        private object ParseOutputFormat(string outputFormatJson)
        {
            if (string.IsNullOrEmpty(outputFormatJson))
            {
                return null;
            }

            try
            {
                var outputFormat = JsonHelper.Deserialize<dynamic>(outputFormatJson);
                return new
                {
                    OutputType = outputFormat.type?.ToString(),
                    Description = outputFormat.description?.ToString(),
                    SchemaJson = outputFormat.schemaJson?.ToString()
                };
            }
            catch
            {
                return null;
            }
        }

        #endregion
    }
}
