import { FlowData } from '../model';

// TreeData 接口定义（用于 VariableTree）
export interface TreeData {
  label: string;
  value: string;
  data: FlowData;
  children?: TreeData[];
}

/**
 * 递归处理变量列表，确保路径信息正确
 * 类似于 VariableTree 中的 recursionList 逻辑
 *
 * @param list 原始变量列表
 * @param parent 父级变量（可选）
 * @returns 处理后的扁平化变量列表
 */
export const processVariableList = (list: FlowData[], parent?: FlowData): FlowData[] => {
  const result: FlowData[] = [];

  for (const sourceItem of list) {
    const item = { ...sourceItem }; // 创建副本避免修改原数据

    // 如果是ROOT节点，直接展开其子节点，不显示ROOT节点本身
    if (item.key === 'ROOT' && item.children && item.children.length > 0) {
      // 递归处理ROOT节点的子节点，但保持父级路径
      const rootChildren = processVariableList(item.children, parent);
      result.push(...rootChildren);
      continue;
    }

    // 构建变量路径，如果父级是ROOT节点，则跳过ROOT
    if (!item.path || item.value?.variableType !== 'global') {
      if (parent && parent.key === 'ROOT') {
        // 如果父级是ROOT节点，直接使用当前节点的key作为路径
        item.path = item.path || item.key;
      } else {
        item.path = parent ? `${parent.path}.${item.key}` : item.key;
      }
    }

    // 构建路径描述，如果父级是ROOT节点，则跳过ROOT前缀
    if (parent && parent.key === 'ROOT') {
      // 如果父级是ROOT节点，直接使用当前节点的描述，不添加ROOT前缀
      item.pathDescription = item.description || item.key;
    } else {
      item.pathDescription = parent
        ? `${parent.pathDescription || parent.key}.${item.description || item.key}`
        : item.description || item.key;
    }

    // 添加当前项到结果
    result.push(item);

    // 递归处理子节点
    if (item.children && item.children.length > 0) {
      const childrenResult = processVariableList(item.children, item);
      result.push(...childrenResult);
    }
  }

  return result;
};

/**
 * 根据变量路径查找变量（在扁平化列表中）
 *
 * @param path 变量路径
 * @param variables 变量列表（已扁平化）
 * @returns 找到的变量或 undefined
 */
export const findVariableByPath = (path: string, variables: FlowData[]): FlowData | undefined => {
  return variables.find((variable) => {
    const variablePath = variable.path || variable.key;
    return variablePath === path || variable.key === path || variable.path === path;
  });
};

/**
 * 递归处理变量列表为 TreeData 格式（用于 VariableTree）
 *
 * @param list 原始变量列表
 * @param parent 父级变量（可选）
 * @returns 处理后的 TreeData 列表
 */
export const processVariableListForTree = (list: FlowData[], parent?: FlowData): TreeData[] => {
  const result: TreeData[] = [];

  for (const sourceItem of list) {
    const item = { ...sourceItem }; // 创建副本避免修改原数据

    // 如果是ROOT节点，直接展开其子节点，不显示ROOT节点本身
    if (item.key === 'ROOT' && item.children && item.children.length > 0) {
      // 递归处理ROOT节点的子节点，但保持父级路径
      const rootChildren = processVariableListForTree(item.children, parent);
      result.push(...rootChildren);
      continue;
    }

    // 构建变量路径，如果父级是ROOT节点，则跳过ROOT
    // 对于全局变量，如果已经有path属性，则使用现有的path，否则构建新的path
    if (!item.path || item.value?.variableType !== 'global') {
      if (parent && parent.key === 'ROOT') {
        // 如果父级是ROOT节点，直接使用当前节点的key作为路径
        item.path = parent.path ? `${parent.path}.${item.key}` : item.key;
      } else {
        item.path = parent ? `${parent.path}.${item.key}` : item.key;
      }
    }

    // 构建路径描述，如果父级是ROOT节点，则跳过ROOT前缀
    if (parent && parent.key === 'ROOT') {
      // 如果父级是ROOT节点，直接使用当前节点的描述，不添加ROOT前缀
      item.pathDescription = item.description || item.key;
    } else {
      item.pathDescription = parent
        ? `${parent.pathDescription || parent.key}.${item.description || item.key}`
        : item.description || item.key;
    }

    // 创建 TreeData 节点
    result.push({
      label: item.key,
      value: item.path,
      data: item,
      children: item.children && item.children.length > 0 ? processVariableListForTree(item.children, item) : [],
    });
  }

  return result;
};
