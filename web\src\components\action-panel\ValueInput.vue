<template>
  <div
    class="simple-value-input"
    :class="{ 'is-disabled': disabled, 'has-value': hasValue }"
    @click="onEdit"
    @contextmenu.prevent="handleContextMenu"
    @mouseover.stop="showEdit = true"
    @mouseleave.stop="showEdit = false"
    @blur="showEdit = false"
  >
    <div
      class="value-content"
      :class="{ 'has-error': hasError }"
      :style="{ 'padding-right': !disabled && showEdit && hasValue ? '18px' : '0' }"
    >
      <!-- 文本值 -->
      <span v-if="dataValue?.type === 'text'" :class="{ 'value-text': true, placeholder: !dataValue.textValue }">
        {{ dataValue.textValue || placeholder }}
      </span>

      <!-- 变量值 -->
      <span v-else-if="dataValue?.type === 'variable'" class="value-text variable" :class="{ invalid: hasError }">
        <t-icon name="link" class="value-icon" />
        {{ displayText }}
        <t-icon v-if="hasError" name="error-circle" class="error-icon" />
      </span>

      <!-- 脚本值 -->
      <span v-else-if="dataValue?.type === 'script'" class="value-text script">
        <t-icon name="code" class="value-icon" />
        {{ dataValue.scriptName || '点击编写脚本' }}
      </span>

      <!-- 可视化值 -->
      <span v-else-if="dataValue?.type === 'visual'" class="value-text visual">
        <t-icon name="view-module" class="value-icon" />
        {{ dataValue.visualName || `${dataValue.visualSteps?.length || 0} 个步骤` }}
      </span>

      <!-- 默认状态 -->
      <span v-else class="value-text placeholder">
        {{ placeholder }}
      </span>
    </div>

    <!-- 操作按钮区域 -->
    <div v-if="!disabled && showEdit && hasValue" class="action-buttons">
      <!-- 清除按钮 -->
      <t-button
        v-if="showReset"
        variant="text"
        size="small"
        @click.stop="onReset"
        :title="'清除'"
        class="action-button"
      >
        <template #icon>
          <close-icon></close-icon>
        </template>
      </t-button>
    </div>

    <!-- 类型标识（可选） -->
    <div v-if="showTypeIndicator && typeText" class="type-badge">
      {{ typeText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import ContextMenu from '@imengyu/vue3-context-menu';
import { useClipboard } from '@vueuse/core';
import { cloneDeep } from 'lodash-es';
import { computed, watch, onMounted, onActivated, ref, h, watchEffect, nextTick } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { FlowData, FlowDataValue } from './model';
import { useVariableReference } from './composables/useVariableReference';
import { useActionFlowStore } from './store';
import { getRandomId } from './utils';
import { CloseIcon, CopyIcon, PasteIcon } from 'tdesign-icons-vue-next';

interface Props {
  dataValue?: FlowDataValue;
  dataType?: string;
  onlyVariable?: boolean;
  limitTypes?: string[];
  placeholder?: string;
  showTypeIndicator?: boolean;
  availableVariables?: FlowData[];
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  showReset?: boolean;
}

interface Emits {
  (e: 'update:data-value', value: FlowDataValue): void;
  (e: 'variable-change', value: FlowDataValue): void;
  (e: 'edit'): void;
  (e: 'reset'): void;
  (e: 'copy', value: FlowDataValue): void;
  (e: 'paste', value: FlowDataValue): void;
}

// 创建默认值
const createDefaultValue = (): FlowDataValue => ({
  type: props.onlyVariable ? 'variable' : 'text',
  dataType: props.dataType || props.limitTypes?.join(',') || '',
  textValue: '',
  variableType: '',
  variableName: '',
  variableValue: '',
  scriptName: '',
  scriptValue: '',
  visualSteps: [],
  visualName: '',
});

const props = withDefaults(defineProps<Props>(), {
  placeholder: '点击设置值',
  showTypeIndicator: false,
  disabled: false,
  size: 'small',
  showReset: true,
});

const emits = defineEmits<Emits>();

// 组件状态
const showEdit = ref(false);

const actionFlowStore = useActionFlowStore();

// 剪贴板功能
const { isSupported, copy } = useClipboard();

// 内部数据值管理（类似ValueInput的实现）
const dataValue = ref<FlowDataValue>(props.dataValue || createDefaultValue());
// 监听 props 变化，同步更新数据值（与ValueInput保持完全一致）
watchEffect(() => {
  if (!props.dataValue) {
    return;
  }
  dataValue.value = props.dataValue;
  if (props.dataType) dataValue.value.dataType = props.dataType;
});

// 计算属性：是否有值
const hasValue = computed(() => {
  const { type, textValue, variableName, variableValue, scriptName, visualName } = dataValue.value;

  switch (type) {
    case 'text':
      return Boolean(textValue);
    case 'variable':
      return Boolean(variableName || variableValue);
    case 'script':
      return Boolean(scriptName);
    case 'visual':
      return Boolean(visualName);
    default:
      return false;
  }
});

// 初始化全局变量（使用 store 中的防抖加载）
const initializeGlobalVariables = async () => {
  try {
    // 使用 store 中的防抖加载方法
    await actionFlowStore.debouncedLoadGlobalVariables();
  } catch (error) {
    console.error('SimpleValueInput: 获取全局变量失败:', error);
  }
};

// 组件挂载时初始化
onMounted(async () => {
  await initializeGlobalVariables();
  // 挂载后强制触发一次变量引用更新和同步
  if (dataValue.value?.type === 'variable') {
    updateVariableReference();
    syncVariableFromSource();
  }
});
onActivated(async () => {
  await initializeGlobalVariables();
  // 激活后强制触发一次变量引用更新和同步
  if (dataValue.value?.type === 'variable') {
    updateVariableReference();
    syncVariableFromSource();
  }
});

// 根据 variableType 获取对应的可用变量列表（使用 store 中的处理逻辑）
const getAvailableVariablesByType = (variableType?: string): FlowData[] => {
  const processedVariables = actionFlowStore.getProcessedVariablesByType(variableType);
  return processedVariables;
};

// 动态计算可用变量列表
const availableVariables = computed(() => {
  const currentDataValue = dataValue.value;

  // // 如果已经提供了 availableVariables，直接使用
  // if (props.availableVariables && props.availableVariables.length > 0) {
  //   return props.availableVariables;
  // }

  // 如果需要自动解析，根据 variableType 获取对应的变量列表
  if (currentDataValue?.type === 'variable') {
    const variableType = currentDataValue.variableType;

    // 如果有具体的 variableType，获取对应类型的变量
    if (variableType) {
      return getAvailableVariablesByType(variableType);
    } else {
      // 如果没有 variableType，返回所有变量供选择
      return getAvailableVariablesByType();
    }
  }

  // 默认返回空数组
  return [];
});

// 使用变量引用管理（支持自动初始化）
const { resolvedVariableInfo, isValidReference, variableType, updateVariableReference } = useVariableReference(
  computed(() => dataValue.value),
  availableVariables,
);

// 根据变量引用ID自动更新变量信息
const syncVariableFromSource = () => {
  if (dataValue.value?.type !== 'variable') return;

  // 如果有变量引用ID，尝试从可用变量中找到对应的源头变量
  if (dataValue.value.variableRefId && availableVariables.value) {
    const refId = dataValue.value.variableRefId;
    // 从引用ID中提取原始变量ID（格式：path_id，例如 "Values_SXxEsWdOmlAIA"）
    const refIdParts = refId.split('_');
    const originalId = refIdParts[refIdParts.length - 1]; // 取最后一部分作为ID

    // 在可用变量中查找对应ID的变量
    let sourceVariable = availableVariables.value.find((t) => t.id == originalId);
    if (!sourceVariable) sourceVariable = findVariableById(originalId, availableVariables.value);

    if (sourceVariable) {
      // 找到源头变量，更新当前变量的信息
      const oldKey = dataValue.value.variableValue;
      const oldName = dataValue.value.variableName;

      // 更新变量信息
      dataValue.value.variableName = sourceVariable.pathDescription || sourceVariable.description || sourceVariable.key;
      dataValue.value.variableValue = sourceVariable.path || sourceVariable.key;

      // 如果信息发生了变化，触发更新事件
      if (oldKey !== dataValue.value.variableValue || oldName !== dataValue.value.variableName) {
        // 触发变更事件
        emits('variable-change', dataValue.value);
      }
    } else {
      // 找不到源头变量，说明变量已被删除
      console.warn('源头变量已被删除:', {
        refId: dataValue.value.variableRefId,
        originalId,
        variableValue: dataValue.value.variableValue,
      });
    }
  }
};

// 递归查找变量
const findVariableById = (id: string, variables: FlowData[]): FlowData | undefined => {
  for (const variable of variables) {
    if (variable.id === id) {
      return variable;
    }
    if (variable.children && variable.children.length > 0) {
      const found = findVariableById(id, variable.children);
      if (found) return found;
    }
  }
  return undefined;
};

// 计算显示文本
const displayText = computed(() => {
  if (dataValue.value?.type === 'variable') {
    // // 优先使用解析后的变量信息
    // if (isValidReference.value && resolvedVariableInfo.value) {
    //   return resolvedVariableInfo.value.name || resolvedVariableInfo.value.path;
    // }

    // 如果有错误，显示错误信息
    if (hasError.value) {
      if (dataValue.value.variableName) {
        return dataValue.value.variableName;
      } else if (dataValue.value.variableValue) {
        return dataValue.value.variableValue;
      }
      return '变量引用失效';
    }

    // 降级到原有的显示方式
    return dataValue.value.variableName || dataValue.value.variableValue || '点击选择变量';
  }
  return '';
});

// 检查是否有错误
const hasError = computed(() => {
  if (dataValue.value?.type !== 'variable') return false;

  // 如果没有设置变量值，不算错误（可能还没选择变量）
  if (!dataValue.value.variableValue && !dataValue.value.variableName) return false;

  // 如果有变量值但引用无效，说明变量引用失效
  if (dataValue.value.variableValue && !isValidReference.value) {
    return true;
  }

  // 如果有引用ID但找不到对应的变量引用，说明变量被删除了
  if (dataValue.value.variableRefId && !resolvedVariableInfo.value) {
    return true;
  }

  return false;
});

// 获取类型文本
const typeText = computed(() => {
  const type = variableType.value || dataValue.value?.dataType || props.dataType;
  if (!type) return '';

  const typeMap: Record<string, string> = {
    string: '文本',
    number: '数字',
    int: '整数',
    decimal: '小数',
    boolean: '布尔',
    object: '对象',
    array: '数组',
    DateTime: '日期',
  };
  return typeMap[type] || type;
});

// 重置数据值
const onReset = () => {
  if (props.disabled) return;

  const defaultValue = createDefaultValue();
  emits('update:data-value', defaultValue);
  emits('variable-change', defaultValue);
  emits('reset');
};

// 复制数据值
const onCopy = async () => {
  if (!hasValue.value) return;

  try {
    const copyData = cloneDeep(dataValue.value);
    const copyText = JSON.stringify(copyData);

    if (isSupported.value) {
      await copy(copyText);
    } else {
      // 降级到 sessionStorage
      sessionStorage.setItem('valueInputCopyText', copyText);
    }

    emits('copy', copyData);
    MessagePlugin.success('复制成功');
  } catch (error) {
    console.error('复制失败:', error);
    MessagePlugin.error('复制失败');
  }
};

// 粘贴数据值
const onPaste = async () => {
  if (props.disabled) return;

  try {
    const sourceText = isSupported.value
      ? await navigator.clipboard.readText()
      : sessionStorage.getItem('valueInputCopyText');

    if (!sourceText) {
      MessagePlugin.warning('剪贴板为空');
      return;
    }

    let pasteData: any;
    try {
      pasteData = JSON.parse(sourceText);
    } catch {
      // 尝试作为纯文本粘贴
      const textValue: FlowDataValue = {
        ...createDefaultValue(),
        type: 'text',
        textValue: sourceText,
      };

      emits('update:data-value', textValue);
      emits('variable-change', textValue);
      emits('paste', textValue);

      MessagePlugin.success('已粘贴为文本');
      return;
    }

    // 验证粘贴数据格式
    if (pasteData && typeof pasteData === 'object' && pasteData.type) {
      const newValue: FlowDataValue = {
        ...createDefaultValue(),
        ...pasteData,
        dataType: props.dataType || props.limitTypes?.join(',') || pasteData.dataType,
      };

      emits('update:data-value', newValue);
      emits('variable-change', newValue);
      emits('paste', newValue);

      MessagePlugin.success('粘贴成功');
    } else {
      MessagePlugin.warning('剪贴板内容格式不正确');
    }
  } catch (error) {
    console.error('粘贴失败:', error);
    MessagePlugin.error('粘贴失败，请检查剪贴板权限');
  }
};

// 处理右键菜单
const handleContextMenu = (e: MouseEvent) => {
  if (props.disabled) return;

  const menuItems = [];

  // 复制选项
  if (hasValue.value) {
    menuItems.push({
      label: '复制',
      icon: h(CopyIcon),
      onClick: () => {
        onCopy();
      },
    });
  }

  // 粘贴选项
  menuItems.push({
    label: '粘贴',
    icon: h(PasteIcon),
    onClick: () => {
      onPaste();
    },
  });

  // 清除选项
  if (props.showReset && hasValue.value) {
    menuItems.push({
      label: '清除',
      icon: h(CloseIcon),
      onClick: () => {
        onReset();
      },
    });
  }

  if (menuItems.length > 0) {
    ContextMenu.showContextMenu({
      theme: 'mac',
      x: e.x,
      y: e.y,
      items: menuItems,
    });
  }
};

// 编辑按钮点击
const valueId = ref('');

const onEdit = () => {
  if (props.disabled) return;

  actionFlowStore.isSaveValue = false;
  actionFlowStore.showValueDialog = true;
  valueId.value = getRandomId();

  const valueToPass = cloneDeep(dataValue.value);

  actionFlowStore.currentValueInputData = {
    id: valueId.value,
    value: valueToPass,
    onlyVariable: props.onlyVariable,
    limitTypes: props.limitTypes,
  };
  emits('edit');
};

// 监听 store 中的保存事件（与ValueInput保持一致）
watch(
  () => actionFlowStore.isSaveValue,
  (newVal, oldVal) => {
    if (newVal !== oldVal && newVal && actionFlowStore.currentValueInputData?.id === valueId.value) {
      const newValue = actionFlowStore.currentValueInputData.value;

      // 发送更新事件给父组件
      emits('update:data-value', newValue);
      emits('variable-change', newValue);

      if (props.dataValue == null || props.dataValue == undefined) {
        nextTick(() => {
          // 避免要保存两次，所以原值为null的时候，推送两次设置好的值
          emits('update:data-value', newValue);
          emits('variable-change', newValue);
        });
      }
    }
  },
);

// 监听变量引用变化
watch(
  () => resolvedVariableInfo.value,
  (newInfo) => {
    if (newInfo && dataValue.value?.type === 'variable') {
      // 当变量信息更新时，触发变更事件
      emits('variable-change', dataValue.value);
    }
  },
  { deep: true, immediate: true },
);

// 监听可用变量变化，自动同步源头变量信息
watch(
  () => availableVariables.value,
  (value) => {
    if (dataValue.value?.type === 'variable') {
      syncVariableFromSource();
    }
  },
  { deep: true, immediate: true },
);
</script>

<style lang="less" scoped>
.simple-value-input {
  display: flex;
  align-items: center;
  padding: 0 8px;
  border: 1px solid transparent;
  border-radius: 3px;
  background: var(--td-bg-color-container);
  min-height: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;

  &:hover {
    border-color: var(--td-border-level-2-color);
    background: var(--td-bg-color-container-hover);
  }

  &:active {
    border-color: var(--td-brand-color);
  }

  // 禁用状态
  &.is-disabled {
    cursor: not-allowed;
    background-color: var(--td-bg-color-component-disabled);
    border-color: var(--td-bg-color-component-disabled);

    .value-text {
      color: var(--td-text-color-disabled);
    }

    &:hover {
      border-color: var(--td-border-level-1-color);
      background-color: var(--td-bg-color-component-disabled);
    }
  }

  // 有值状态
  // &.has-value {
  //   border-color: var(--td-border-level-1-color);
  // }

  // 操作按钮区域
  .action-buttons {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0;
    margin-left: 4px;
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
  }

  .action-button {
    padding: 0;
    min-width: auto;
    width: 16px;
    height: 16px;

    :deep(.t-button__icon) {
      font-size: 12px;
    }

    &:hover {
      background-color: var(--td-bg-color-container-hover);
    }
  }

  .value-content {
    flex: 1;
    display: flex;
    align-items: center;
    min-height: 20px;
    min-width: 0; // 确保可以收缩

    &.has-error {
      .value-text.invalid {
        color: var(--td-error-color);
      }
    }

    .value-text {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: var(--td-text-color-primary);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      min-width: 0; // 确保可以收缩

      &.placeholder {
        color: var(--td-text-color-placeholder);
      }

      &.variable {
        color: var(--td-brand-color);

        &.invalid {
          color: var(--td-error-color);
          text-decoration: line-through;
        }
      }

      &.script {
        color: var(--td-warning-color);
      }

      &.visual {
        color: var(--td-success-color);
      }

      .value-icon {
        font-size: 12px;
        flex-shrink: 0;
      }

      .error-icon {
        color: var(--td-error-color);
        font-size: 12px;
        flex-shrink: 0;
      }
    }
  }

  .type-badge {
    flex-shrink: 0;
    padding: 2px 6px;
    background: var(--td-bg-color-tag);
    color: var(--td-text-color-secondary);
    font-size: 12px;
    border-radius: 3px;
    line-height: 1;
    margin-left: 4px;
  }
}
</style>
