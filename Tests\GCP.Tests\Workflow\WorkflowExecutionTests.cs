using Xunit;
using Xunit.Abstractions;
using FluentAssertions;
using GCP.Tests.Infrastructure;
using GCP.Functions.Common.Services;
using GCP.DataAccess;
using GCP.Common;
using GCP.FunctionPool;
using GCP.FunctionPool.Flow;
using LinqToDB;
using Microsoft.Extensions.DependencyInjection;

namespace GCP.Tests.Workflow
{
    /// <summary>
    /// 工作流执行测试类
    /// </summary>
    public class WorkflowExecutionTests : DatabaseTestBase
    {
        public WorkflowExecutionTests(ITestOutputHelper output) : base(output)
        {
        }

        protected override void ConfigureTestServices(IServiceCollection services)
        {
            base.ConfigureTestServices(services);
            services.AddScoped<FlowRunService>();
            services.AddScoped<FlowHistoryService>();
        }

        [Fact]
        public async Task SimpleWorkflow1_ShouldExecuteSuccessfully()
        {
            // Act
            var executor = new FlowExecutor(""""
                                            { "data": [ { "id": "iHwHiYoyBHgvR", "type": "string", "key": "eid", "isCustomize": true, "value": { "type": "text", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "1", "scriptName": "", "scriptValue": "" }, "path": "eid", "pathDescription": "企业ID", "description": "企业ID", "required": false }, { "id": "cZipnKauHNiPC", "type": "string", "key": "oid", "isCustomize": true, "value": { "type": "text", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "1", "scriptName": "", "scriptValue": "" }, "path": "oid", "pathDescription": "组织ID", "description": "组织ID", "required": false }, { "id": "muqsNnwWaWRNH", "type": "string", "key": "erpOrgId", "isCustomize": true, "value": { "type": "text", "variableType": "", "variableName": "", "variableValue": "", "textValue": "1001W1100000000008U2", "scriptName": "", "scriptValue": "" }, "description": "ERP组织ID", "path": "erpOrgId", "pathDescription": "ERP组织ID", "required": false }, { "id": "JOB_LAST_TIME", "key": "JOB_LAST_TIME", "description": "JOB上次执行时间", "type": "DateTime", "value": { "type": "variable", "variableType": "current", "variableName": "获取JOB上次执行时间", "variableValue": "_JOB_LAST_TIME" }, "isCustomize": false, "required": false, "path": "JOB_LAST_TIME", "pathDescription": "JOB上次执行时间" } ], "body": [ { "id": "JYsDLBmkZhrbv", "name": "当前运行时间减去半小时", "function": "runScript", "controlType": null, "config": { "name": "运行脚本", "function": "runScript", "icon": "code", "componentName": "./9_Other/5_Script" }, "nextId": "PUSlquSTajecH", "args": { "name": "当前运行时间减去半小时", "description": "", "script": "// 假设 _data.JOB_LAST_TIME 是 ISO 字符串（如 \"2024-05-20T14:30:00\"）\r\nconst originalTimeStr = _data.JOB_LAST_TIME;\r\n\r\n// 解析为 Date 对象\r\nconst originalDate = new Date(originalTimeStr);\r\n\r\n// 检查是否为有效日期\r\nif (!isNaN(originalDate.getTime())) {\r\n    // 减去 30 分钟（30 * 60 * 1000 毫秒）\r\n    _data.JOB_LAST_TIME = new Date(originalDate.getTime() - 30 * 60 * 1000);\r\n} \r\n" } }, { "id": "PUSlquSTajecH", "name": "ERP物料查询", "function": "dataQuery", "controlType": "forEach", "config": { "name": "数据库自动查询", "function": "dataQuery", "icon": "data-base", "controlType": "forEach", "endStepName": "结束查询", "componentName": "./1_DataBase/1_DataQuery" }, "args": { "name": "ERP物料查询", "dataSource": "228147909508710400", "autoPaged": true, "isPaging": false, "hasTotal": false, "pageSize": { "type": "text", "textValue": "500" }, "description": "", "operateType": "configure", "sqlInfo": { "sql": "", "parameters": [] }, "configureInfo": [ { "id": "root", "tableData": { "tableName": "WMS_DOC", "tableDescription": null, "columns": [ { "columnName": "PK_INVMANDOC", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "PK_CORP", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "INVCODE", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "INVNAME", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "INVSPEC", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "INVCLASSCODE", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "INVCLASSNAME", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "SHORTNAME", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "MEASNAME", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "WHOLEMANAFLAG", "description": null, "dataType": "char", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "STORCODE", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "STORNAME", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "DEF3", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "DEF6", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "DEF4", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "STORCODE1", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "STORNAME1", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "DR", "description": null, "dataType": "long", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "TS", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnValue": null, "columnName": "PK_CALBODY", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false }, { "columnValue": null, "columnName": "BODYNAME", "description": null, "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false } ], "sortColumns": [ { "id": "hQzefSXXnqZFE", "columnName": "PK_INVMANDOC", "order": "ASC" } ], "conditions": { "id": "root", "type": "node", "operator": "AND", "children": [ { "id": "first", "type": "leaf", "operator": "GreaterThanOrEqual", "column": "TS", "value": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "JOB_LAST_TIME", "textValue": "", "scriptName": "时间转换文本", "scriptValue": "Utils.DATE_TO_STRING(_data.JOB_LAST_TIME, \"yyyy-MM-dd HH:mm:ss\")" } }, { "id": "XKkaUJlsUuvhn", "type": "leaf", "operator": "Equals", "isFilter": false, "column": "PK_CALBODY", "value": { "type": "variable", "dataType": "string", "variableType": "local", "variableName": "ERP组织ID", "variableValue": "erpOrgId", "textValue": "", "scriptName": "", "scriptValue": "" } } ] } } } ] }, "control": { "forEach": { "async": false, "item": [ { "id": "list", "key": "list", "description": "结果", "type": "array", "value": { "type": "variable", "variableType": "current", "variableValue": "result" }, "children": [ { "id": "PK_INVMANDOC", "key": "PK_INVMANDOC", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.PK_INVMANDOC" }, "path": "PUSlquSTajecH.list.PK_INVMANDOC", "pathDescription": "ERP物料查询.结果.PK_INVMANDOC" }, { "id": "PK_CORP", "key": "PK_CORP", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.PK_CORP" }, "path": "PUSlquSTajecH.list.PK_CORP", "pathDescription": "ERP物料查询.结果.PK_CORP" }, { "id": "INVCODE", "key": "INVCODE", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.INVCODE" }, "path": "PUSlquSTajecH.list.INVCODE", "pathDescription": "ERP物料查询.结果.INVCODE" }, { "id": "INVNAME", "key": "INVNAME", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.INVNAME" }, "path": "PUSlquSTajecH.list.INVNAME", "pathDescription": "ERP物料查询.结果.INVNAME" }, { "id": "INVSPEC", "key": "INVSPEC", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.INVSPEC" }, "path": "PUSlquSTajecH.list.INVSPEC", "pathDescription": "ERP物料查询.结果.INVSPEC" }, { "id": "INVCLASSCODE", "key": "INVCLASSCODE", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.INVCLASSCODE" }, "path": "PUSlquSTajecH.list.INVCLASSCODE", "pathDescription": "ERP物料查询.结果.INVCLASSCODE" }, { "id": "INVCLASSNAME", "key": "INVCLASSNAME", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.INVCLASSNAME" }, "path": "PUSlquSTajecH.list.INVCLASSNAME", "pathDescription": "ERP物料查询.结果.INVCLASSNAME" }, { "id": "SHORTNAME", "key": "SHORTNAME", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.SHORTNAME" }, "path": "PUSlquSTajecH.list.SHORTNAME", "pathDescription": "ERP物料查询.结果.SHORTNAME" }, { "id": "MEASNAME", "key": "MEASNAME", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.MEASNAME" }, "path": "PUSlquSTajecH.list.MEASNAME", "pathDescription": "ERP物料查询.结果.MEASNAME" }, { "id": "WHOLEMANAFLAG", "key": "WHOLEMANAFLAG", "description": null, "type": "char", "value": { "type": "variable", "dataType": "char", "variableType": "current", "variableValue": "item.WHOLEMANAFLAG" }, "path": "PUSlquSTajecH.list.WHOLEMANAFLAG", "pathDescription": "ERP物料查询.结果.WHOLEMANAFLAG" }, { "id": "STORCODE", "key": "STORCODE", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.STORCODE" }, "path": "PUSlquSTajecH.list.STORCODE", "pathDescription": "ERP物料查询.结果.STORCODE" }, { "id": "STORNAME", "key": "STORNAME", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.STORNAME" }, "path": "PUSlquSTajecH.list.STORNAME", "pathDescription": "ERP物料查询.结果.STORNAME" }, { "id": "DEF3", "key": "DEF3", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.DEF3" }, "path": "PUSlquSTajecH.list.DEF3", "pathDescription": "ERP物料查询.结果.DEF3" }, { "id": "DEF6", "key": "DEF6", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.DEF6" }, "path": "PUSlquSTajecH.list.DEF6", "pathDescription": "ERP物料查询.结果.DEF6" }, { "id": "DEF4", "key": "DEF4", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.DEF4" }, "path": "PUSlquSTajecH.list.DEF4", "pathDescription": "ERP物料查询.结果.DEF4" }, { "id": "STORCODE1", "key": "STORCODE1", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.STORCODE1" }, "path": "PUSlquSTajecH.list.STORCODE1", "pathDescription": "ERP物料查询.结果.STORCODE1" }, { "id": "STORNAME1", "key": "STORNAME1", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.STORNAME1" }, "path": "PUSlquSTajecH.list.STORNAME1", "pathDescription": "ERP物料查询.结果.STORNAME1" }, { "id": "DR", "key": "DR", "description": null, "type": "long", "value": { "type": "variable", "dataType": "long", "variableType": "current", "variableValue": "item.DR" }, "path": "PUSlquSTajecH.list.DR", "pathDescription": "ERP物料查询.结果.DR" }, { "id": "TS", "key": "TS", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.TS" }, "path": "PUSlquSTajecH.list.TS", "pathDescription": "ERP物料查询.结果.TS" }, { "id": "PK_CALBODY", "key": "PK_CALBODY", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.PK_CALBODY" }, "path": "PUSlquSTajecH.list.PK_CALBODY", "pathDescription": "ERP物料查询.结果.PK_CALBODY" }, { "id": "BODYNAME", "key": "BODYNAME", "description": null, "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.BODYNAME" }, "path": "PUSlquSTajecH.list.BODYNAME", "pathDescription": "ERP物料查询.结果.BODYNAME" } ], "path": "PUSlquSTajecH.list", "pathDescription": "ERP物料查询.结果" } ], "list": "", "nextId": "HCDdDIMrIvgog" } } }, { "id": "CgCXeYmHvesyi", "name": "WMS物料保存", "function": "dataSave", "controlType": null, "config": { "name": "数据库保存", "function": "dataSave", "icon": "data-base", "componentName": "./1_DataBase/2_DataSave" }, "args": { "name": "WMS物料保存", "dataSource": "228147443701891072", "sourceDataPath": { "type": "variable", "dataType": "array", "variableType": "local", "variableName": "ERP物料查询.结果", "variableValue": "PUSlquSTajecH.list", "textValue": "", "scriptName": "", "scriptValue": "" }, "description": "", "operateType": "Save", "configureInfo": { "tableName": "m_mitem", "tableDescription": "物料", "columns": [ { "columnName": "id", "description": "", "dataType": "long", "required": true, "isCustomize": false, "isPrimaryKey": true, "isUpdate": false, "isInsert": true, "isCondition": false, "columnValue": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "", "scriptName": "随机ID", "scriptValue": "Utils.UUID()" } }, { "columnName": "time_create", "description": "创建时间", "dataType": "DateTime", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false, "columnValue": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "", "scriptName": "当前时间", "scriptValue": "Utils.NOW()" } }, { "columnName": "creator", "description": "创建人", "dataType": "string", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false, "columnValue": { "type": "text", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "gcp", "scriptName": "", "scriptValue": "" } }, { "columnName": "time_modified", "description": "修改时间", "dataType": "DateTime", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": false, "isCondition": false, "columnValue": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "", "scriptName": "当前时间", "scriptValue": "Utils.NOW()" } }, { "columnName": "modifier", "description": "修改人", "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": false, "isCondition": false, "columnValue": { "type": "text", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "gcp", "scriptName": "", "scriptValue": "" } }, { "columnName": "state", "description": "1可用；0禁用", "dataType": "int", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": false, "columnValue": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "dataSourceItem.DR", "textValue": "", "scriptName": "如果判断", "scriptValue": "Utils.IF(dataSourceItem.DR == 0, 1, 0)" } }, { "columnName": "eid", "description": "企业ID", "dataType": "long", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": true, "columnValue": { "type": "variable", "dataType": "", "variableType": "local", "variableName": "企业ID", "variableValue": "eid", "textValue": "", "scriptName": "", "scriptValue": "" } }, { "columnName": "mitem_code", "description": "物料代码", "dataType": "string", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": true, "columnValue": { "type": "variable", "dataType": "", "variableType": "current", "variableName": "数据集.INVCODE", "variableValue": "dataSourceItem.INVCODE", "textValue": "", "scriptName": "", "scriptValue": "" } }, { "columnName": "mitem_name", "description": "物料名称", "dataType": "string", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": false, "columnValue": { "type": "variable", "dataType": "", "variableType": "current", "variableName": "数据集.INVNAME", "variableValue": "dataSourceItem.INVNAME", "textValue": "", "scriptName": "", "scriptValue": "" } }, { "columnName": "mitem_desc", "description": "物料描述", "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": false, "columnValue": { "type": "variable", "dataType": "string", "variableType": "current", "variableName": "数据集.INVNAME", "variableValue": "dataSourceItem.INVNAME", "textValue": "", "scriptName": "", "scriptValue": "" } }, { "columnName": "uom", "description": "单位", "dataType": "string", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": false, "columnValue": { "type": "variable", "dataType": "", "variableType": "current", "variableName": "数据集.MEASNAME", "variableValue": "dataSourceItem.MEASNAME", "textValue": "", "scriptName": "", "scriptValue": "" } }, { "columnName": "shelf_life_days", "description": "保质期天数", "dataType": "int", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false } ], "sortColumns": [], "conditions": null } }, "result": [ { "id": "list", "key": "list", "description": "结果", "type": "array", "value": { "type": "variable", "variableType": "current", "variableValue": "result" }, "children": [ { "id": "id", "key": "id", "description": "", "type": "long", "value": { "type": "variable", "dataType": "long", "variableType": "current", "variableValue": "item.id" }, "path": "CgCXeYmHvesyi.list.id", "pathDescription": "WMS物料保存.结果.id" }, { "id": "eid", "key": "eid", "description": "企业ID", "type": "long", "value": { "type": "variable", "dataType": "long", "variableType": "current", "variableValue": "item.eid" }, "path": "CgCXeYmHvesyi.list.eid", "pathDescription": "WMS物料保存.结果.企业ID" }, { "id": "mitem_code", "key": "mitem_code", "description": "物料代码", "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.mitem_code" }, "path": "CgCXeYmHvesyi.list.mitem_code", "pathDescription": "WMS物料保存.结果.物料代码" } ], "path": "CgCXeYmHvesyi.list", "pathDescription": "WMS物料保存.结果" }, { "id": "qfHNkTbXVPTbO", "type": "object", "key": "dic", "isCustomize": true, "description": "物料ID字典", "value": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "", "scriptName": "生成字典", "scriptValue": "var dic = {};\r\nresult.forEach(item => {\r\n    dic[item.eid + '|' + item.mitem_code] = item.id;\r\n});\r\nreturn dic;" }, "path": "CgCXeYmHvesyi.dic", "pathDescription": "WMS物料保存.物料ID字典" } ], "nextId": "MRQuPHTcelTsz" }, { "id": "MRQuPHTcelTsz", "name": "WMS物料分类保存", "function": "dataSave", "controlType": null, "config": { "name": "数据库保存", "function": "dataSave", "icon": "data-base", "componentName": "./1_DataBase/2_DataSave" }, "args": { "name": "WMS物料分类保存", "dataSource": "228147443701891072", "sourceDataPath": { "type": "variable", "dataType": "array", "variableType": "local", "variableName": "ERP物料查询.结果", "variableValue": "PUSlquSTajecH.list", "textValue": "", "scriptName": "", "scriptValue": "" }, "description": "", "operateType": "Save", "configureInfo": { "tableName": "m_mitem_category", "tableDescription": "物料分类", "columns": [ { "columnName": "id", "description": "", "dataType": "long", "required": true, "isCustomize": false, "isPrimaryKey": true, "isUpdate": false, "isInsert": true, "isCondition": false, "columnValue": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "", "scriptName": "随机ID", "scriptValue": "Utils.UUID()" } }, { "columnName": "time_create", "description": "创建时间", "dataType": "DateTime", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false, "columnValue": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "", "scriptName": "当前时间", "scriptValue": "Utils.NOW()" } }, { "columnName": "creator", "description": "创建人", "dataType": "string", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false, "columnValue": { "type": "text", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "gcp", "scriptName": "", "scriptValue": "" } }, { "columnName": "time_modified", "description": "修改时间", "dataType": "DateTime", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": false, "isCondition": false, "columnValue": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "", "scriptName": "当前时间", "scriptValue": "Utils.NOW()" } }, { "columnName": "modifier", "description": "修改人", "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": false, "isCondition": false, "columnValue": { "type": "text", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "gcp", "scriptName": "", "scriptValue": "" } }, { "columnName": "state", "description": "1可用；0禁用", "dataType": "int", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false, "columnValue": { "type": "text", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "1", "scriptName": "", "scriptValue": "" } }, { "columnName": "eid", "description": "企业ID", "dataType": "long", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": true, "columnValue": { "type": "variable", "dataType": "", "variableType": "local", "variableName": "企业ID", "variableValue": "eid", "textValue": "", "scriptName": "", "scriptValue": "" } }, { "columnName": "oid", "description": "组织ID", "dataType": "long", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": true, "columnValue": { "type": "variable", "dataType": "", "variableType": "local", "variableName": "组织ID", "variableValue": "oid", "textValue": "", "scriptName": "", "scriptValue": "" } }, { "columnName": "category_code", "description": "物料分类代码", "dataType": "string", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": true, "columnValue": { "type": "variable", "dataType": "", "variableType": "current", "variableName": "数据集.INVCLASSCODE", "variableValue": "dataSourceItem.INVCLASSCODE", "textValue": "", "scriptName": "", "scriptValue": "" } }, { "columnName": "category_name", "description": "物料分类名称", "dataType": "string", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": false, "columnValue": { "type": "variable", "dataType": "", "variableType": "current", "variableName": "数据集.INVCLASSNAME", "variableValue": "dataSourceItem.INVCLASSNAME", "textValue": "", "scriptName": "", "scriptValue": "" } }, { "columnName": "category_desc", "description": "物料分类描述", "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": false, "columnValue": { "type": "variable", "dataType": "string", "variableType": "current", "variableName": "数据集.INVCLASSNAME", "variableValue": "dataSourceItem.INVCLASSNAME", "textValue": "", "scriptName": "", "scriptValue": "" } }, { "columnName": "req_calc_rule", "description": "小数位计算类型", "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false }, { "columnName": "onboard_rule_code", "description": "物料投料规则", "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false }, { "columnName": "is_manual", "description": "是否手工创建", "dataType": "int", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": false, "columnValue": { "type": "text", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "0", "scriptName": "", "scriptValue": "" } } ], "sortColumns": [], "conditions": null } }, "result": [ { "id": "list", "key": "list", "description": "结果", "type": "array", "value": { "type": "variable", "variableType": "current", "variableValue": "result" }, "children": [ { "id": "id", "key": "id", "description": "", "type": "long", "value": { "type": "variable", "dataType": "long", "variableType": "current", "variableValue": "item.id" }, "path": "MRQuPHTcelTsz.list.id", "pathDescription": "WMS物料分类保存.结果.id" }, { "id": "eid", "key": "eid", "description": "企业ID", "type": "long", "value": { "type": "variable", "dataType": "long", "variableType": "current", "variableValue": "item.eid" }, "path": "MRQuPHTcelTsz.list.eid", "pathDescription": "WMS物料分类保存.结果.企业ID" }, { "id": "oid", "key": "oid", "description": "组织ID", "type": "long", "value": { "type": "variable", "dataType": "long", "variableType": "current", "variableValue": "item.oid" }, "path": "MRQuPHTcelTsz.list.oid", "pathDescription": "WMS物料分类保存.结果.组织ID" }, { "id": "category_code", "key": "category_code", "description": "物料分类代码", "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.category_code" }, "path": "MRQuPHTcelTsz.list.category_code", "pathDescription": "WMS物料分类保存.结果.物料分类代码" } ], "path": "MRQuPHTcelTsz.list", "pathDescription": "WMS物料分类保存.结果" }, { "id": "JbGuEXFDeQqWm", "type": "object", "key": "dic", "isCustomize": true, "description": "物料分类ID字典", "value": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "", "scriptName": "生成字典", "scriptValue": "var dic = {};\r\nresult.forEach(item => {\r\n    dic[item.eid + '|' + item.oid + '|' + item.category_code] = item.id;\r\n});\r\nreturn dic;" }, "path": "MRQuPHTcelTsz.dic", "pathDescription": "WMS物料分类保存.物料分类ID字典" } ], "nextId": "NygmdfMdZYuka" }, { "id": "NygmdfMdZYuka", "name": "WMS物料组织保存", "function": "dataSave", "controlType": null, "config": { "name": "数据库保存", "function": "dataSave", "icon": "data-base", "componentName": "./1_DataBase/2_DataSave" }, "args": { "name": "WMS物料组织保存", "dataSource": "228147443701891072", "sourceDataPath": { "type": "variable", "dataType": "array", "variableType": "local", "variableName": "ERP物料查询.结果", "variableValue": "PUSlquSTajecH.list", "textValue": "", "scriptName": "", "scriptValue": "" }, "description": "", "exceptionSkip": false, "operateType": "Save", "configureInfo": { "tableName": "m_mitem_in_org", "tableDescription": "物料与组织关系表", "columns": [ { "columnName": "id", "description": "", "dataType": "long", "required": true, "isCustomize": false, "isPrimaryKey": true, "isUpdate": false, "isInsert": true, "isCondition": false, "columnValue": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "", "scriptName": "随机ID", "scriptValue": "Utils.UUID()" } }, { "columnName": "time_create", "description": "创建时间", "dataType": "DateTime", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false, "columnValue": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "", "scriptName": "当前时间", "scriptValue": "Utils.NOW()" } }, { "columnName": "creator", "description": "创建人", "dataType": "string", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false, "columnValue": { "type": "text", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "gcp", "scriptName": "", "scriptValue": "" } }, { "columnName": "time_modified", "description": "修改时间", "dataType": "DateTime", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": false, "isCondition": false, "columnValue": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "", "scriptName": "当前时间", "scriptValue": "Utils.NOW()" } }, { "columnName": "modifier", "description": "修改人", "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": false, "isCondition": false, "columnValue": { "type": "text", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "gcp", "scriptName": "", "scriptValue": "" } }, { "columnName": "state", "description": "1可用；0禁用", "dataType": "int", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false, "columnValue": { "type": "text", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "1", "scriptName": "", "scriptValue": "" } }, { "columnName": "eid", "description": "企业ID", "dataType": "long", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": true, "columnValue": { "type": "variable", "dataType": "", "variableType": "local", "variableName": "企业ID", "variableValue": "eid", "textValue": "", "scriptName": "", "scriptValue": "" } }, { "columnName": "s_org_id", "description": "组织ID", "dataType": "long", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": true, "columnValue": { "type": "variable", "dataType": "", "variableType": "local", "variableName": "组织ID", "variableValue": "oid", "textValue": "", "scriptName": "", "scriptValue": "" } }, { "columnName": "m_mitem_id", "description": "物料ID", "dataType": "long", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": true, "columnValue": { "type": "script", "dataType": "", "variableType": "local", "variableName": "WMS物料保存.物料ID字典", "variableValue": "dataSourceItem.INVCODE", "textValue": "", "scriptName": "物料字典取数", "scriptValue": "_data.CgCXeYmHvesyi.dic[_data.eid + '|' + dataSourceItem.INVCODE]" } }, { "columnName": "m_mitem_category_id", "description": "物料分类ID", "dataType": "long", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": false, "columnValue": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "dataSourceItem.INVCLASSCODE", "textValue": "", "scriptName": "物料分类字典取数", "scriptValue": "_data.MRQuPHTcelTsz.dic[_data.eid + '|' + _data.oid + '|' + dataSourceItem.INVCLASSCODE]" } }, { "columnName": "w_warehouse_id", "description": "默认仓库ID", "dataType": "long", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": false, "columnValue": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "dataSourceItem.STORCODE1", "textValue": "", "scriptName": "仓库字典取数", "scriptValue": "_data.HCDdDIMrIvgog.dic[_data.eid + '|' + _data.oid + '|' + dataSourceItem.STORCODE1]" } }, { "columnName": "supply_category", "description": "供应方式", "dataType": "string", "required": true, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false, "columnValue": { "type": "text", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "拉式", "scriptName": "", "scriptValue": "" } }, { "columnName": "is_product", "description": "是否成品，1：是；0：否", "dataType": "int", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false }, { "columnName": "is_raw", "description": "是否原材料,1：是；0：否", "dataType": "int", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false }, { "columnName": "is_in_process", "description": "是否半成品,1：是；0：否", "dataType": "int", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false }, { "columnName": "is_batch_no", "description": "是否启用批次,1：是；0：否", "dataType": "int", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": false, "columnValue": { "type": "text", "dataType": "", "variableType": "", "variableName": "", "variableValue": "dataSourceItem.WHOLEMANAFLAG", "textValue": "0", "scriptName": "如果判断", "scriptValue": "" } }, { "columnName": "specifications_qty", "description": "配送卡规格数量", "dataType": "decimal", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false }, { "columnName": "customer_code", "description": "客户编码", "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": false, "columnValue": { "type": "variable", "dataType": "", "variableType": "current", "variableName": "数据集.INVSPEC", "variableValue": "dataSourceItem.INVSPEC", "textValue": "", "scriptName": "", "scriptValue": "" } }, { "columnName": "min_packaging_qty", "description": "最小包装单元", "dataType": "decimal", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false }, { "columnName": "receipt_warehouse_id", "description": "收货仓库ID", "dataType": "long", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": true, "isInsert": true, "isCondition": false, "columnValue": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "", "scriptName": "仓库字典取数", "scriptValue": "_data.HCDdDIMrIvgog.dic[_data.eid + '|' + _data.oid + '|' + dataSourceItem.STORCODE]" } }, { "columnValue": null, "columnName": "is_comlete_inspection", "description": "是否完工入库检验", "dataType": "int", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false }, { "columnValue": null, "columnName": "is_process_part", "description": "是否工序件", "dataType": "int", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false }, { "columnValue": null, "columnName": "print_model", "description": "打印模式(提前打印:PRE_PRINT,在线打印:ONLINE_PRINT)", "dataType": "string", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false }, { "columnValue": null, "columnName": "is_Key_components", "description": "是否关键件", "dataType": "int", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false }, { "columnValue": { "type": "text", "variableType": "", "variableName": "", "variableValue": "", "textValue": "1", "scriptName": "", "scriptValue": "" }, "columnName": "is_first_check", "description": "是否首检", "dataType": "int", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false }, { "columnValue": { "type": "text", "variableType": "", "variableName": "", "variableValue": "", "textValue": "1", "scriptName": "", "scriptValue": "" }, "columnName": "is_spot_check", "description": "是否点检", "dataType": "int", "required": false, "isCustomize": false, "isPrimaryKey": false, "isUpdate": false, "isInsert": true, "isCondition": false } ], "sortColumns": [], "conditions": null }, "sourceUpdateInfo": {} }, "result": [ { "id": "list", "key": "list", "description": "结果", "type": "array", "value": { "type": "variable", "variableType": "current", "variableValue": "result" }, "children": [ { "id": "id", "key": "id", "description": "", "type": "long", "value": { "type": "variable", "dataType": "long", "variableType": "current", "variableValue": "result.id" }, "required": false }, { "id": "eid", "key": "eid", "description": "企业ID", "type": "long", "value": { "type": "variable", "dataType": "long", "variableType": "current", "variableValue": "result.eid" }, "required": false }, { "id": "s_org_id", "key": "s_org_id", "description": "组织ID", "type": "long", "value": { "type": "variable", "dataType": "long", "variableType": "current", "variableValue": "result.s_org_id" }, "required": false }, { "id": "m_mitem_id", "key": "m_mitem_id", "description": "物料ID", "type": "long", "value": { "type": "variable", "dataType": "long", "variableType": "current", "variableValue": "result.m_mitem_id" }, "required": false } ], "required": false } ] }, { "id": "HCDdDIMrIvgog", "name": "WMS仓库字典", "function": "dataCustomizeQuery", "controlType": null, "config": { "name": "数据库查询", "function": "dataCustomizeQuery", "icon": "data-base", "componentName": "./1_DataBase/1_DataCustomizeQuery" }, "args": { "name": "WMS仓库字典", "dataSource": "228147443701891072", "autoPaged": false, "isPaging": false, "hasTotal": false, "pageSize": { "type": "text", "textValue": "500" }, "pageIndex": { "type": "text", "textValue": "1" }, "description": "", "operateType": "configure", "sqlInfo": { "sql": "", "parameters": [] }, "configureInfo": [ { "id": "root", "tableData": { "tableName": "m_warehouse", "tableDescription": "仓库", "columns": [ { "columnName": "id", "description": "", "dataType": "long", "required": true, "isCustomize": false, "isPrimaryKey": true }, { "columnName": "state", "description": "1可用；0禁用", "dataType": "int", "required": true, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "eid", "description": "企业ID", "dataType": "long", "required": true, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "oid", "description": "组织ID", "dataType": "long", "required": true, "isCustomize": false, "isPrimaryKey": false }, { "columnName": "warehouse_code", "description": "仓库代码", "dataType": "string", "required": true, "isCustomize": false, "isPrimaryKey": false } ], "sortColumns": [], "conditions": { "id": "root", "type": "node", "operator": "AND", "children": [ { "id": "first", "type": "leaf", "operator": "Equals", "column": "state", "value": { "type": "text", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "1", "scriptName": "", "scriptValue": "" } }, { "id": "KoxFEOdsuwqfd", "type": "leaf", "operator": "Equals", "column": "eid", "value": { "type": "variable", "dataType": "", "variableType": "local", "variableName": "企业ID", "variableValue": "eid", "textValue": "", "scriptName": "", "scriptValue": "" } }, { "id": "GQWPULSFOxBHU", "type": "leaf", "operator": "Equals", "column": "oid", "value": { "type": "variable", "dataType": "", "variableType": "local", "variableName": "组织ID", "variableValue": "oid", "textValue": "", "scriptName": "", "scriptValue": "" } } ] } } } ] }, "result": [ { "id": "list", "key": "list", "description": "结果", "type": "array", "value": { "type": "variable", "variableType": "current", "variableValue": "result" }, "children": [ { "id": "id", "key": "id", "description": "", "type": "long", "value": { "type": "variable", "dataType": "long", "variableType": "current", "variableValue": "item.id" }, "path": "HCDdDIMrIvgog.list.id", "pathDescription": "WMS仓库字典.结果.id" }, { "id": "state", "key": "state", "description": "1可用；0禁用", "type": "int", "value": { "type": "variable", "dataType": "int", "variableType": "current", "variableValue": "item.state" }, "path": "HCDdDIMrIvgog.list.state", "pathDescription": "WMS仓库字典.结果.1可用；0禁用" }, { "id": "eid", "key": "eid", "description": "企业ID", "type": "long", "value": { "type": "variable", "dataType": "long", "variableType": "current", "variableValue": "item.eid" }, "path": "HCDdDIMrIvgog.list.eid", "pathDescription": "WMS仓库字典.结果.企业ID" }, { "id": "oid", "key": "oid", "description": "组织ID", "type": "long", "value": { "type": "variable", "dataType": "long", "variableType": "current", "variableValue": "item.oid" }, "path": "HCDdDIMrIvgog.list.oid", "pathDescription": "WMS仓库字典.结果.组织ID" }, { "id": "warehouse_code", "key": "warehouse_code", "description": "仓库代码", "type": "string", "value": { "type": "variable", "dataType": "string", "variableType": "current", "variableValue": "item.warehouse_code" }, "path": "HCDdDIMrIvgog.list.warehouse_code", "pathDescription": "WMS仓库字典.结果.仓库代码" } ], "path": "HCDdDIMrIvgog.list", "pathDescription": "WMS仓库字典.结果" }, { "id": "NQDcVCiJLXAEB", "type": "object", "key": "dic", "isCustomize": true, "value": { "type": "script", "dataType": "", "variableType": "", "variableName": "", "variableValue": "", "textValue": "", "scriptName": "生成字典", "scriptValue": "var dic = {};\r\nresult.forEach(item => {\r\n    dic[item.eid + '|' + item.oid + '|' + item.warehouse_code] = item.id;\r\n});\r\nreturn dic;" }, "description": "仓库ID字典", "path": "HCDdDIMrIvgog.dic", "pathDescription": "WMS仓库字典.仓库ID字典" } ], "nextId": "CgCXeYmHvesyi" } ] }
                                            
                                            """");
            var context = CreateTestContext();
            context.Args["_JOB_LAST_TIME"] = DateTime.Parse("2025-07-23 19:53:43");
            var result = await executor.Run(context);

            // Assert
            // 对于只包含controlDelay的简单工作流，结果可能为null，这是正常的
            // 重要的是工作流能够成功执行而不抛出异常
            context.Should().NotBeNull("工作流上下文应该存在");
            Output.WriteLine($"工作流执行结果: {JsonHelper.Serialize(result ?? "null")}");
        }

        [Fact]
        public async Task SimpleWorkflow_ShouldExecuteSuccessfully()
        {
            // Arrange
            await InitializeTestDataAsync();
            var (function, functionCode) = await CreateTestFunctionAsync();

            // Act
            var executor = new FlowExecutor(functionCode.Code);
            var context = CreateTestContext();

            var result = await executor.Run(context);

            // Assert
            // 对于只包含controlDelay的简单工作流，结果可能为null，这是正常的
            // 重要的是工作流能够成功执行而不抛出异常
            context.Should().NotBeNull("工作流上下文应该存在");
            Output.WriteLine($"工作流执行结果: {JsonHelper.Serialize(result ?? "null")}");
        }

        [Fact]
        public async Task ComplexWorkflow_WithBranching_ShouldExecuteCorrectly()
        {
            // Arrange
            await InitializeTestDataAsync();
            var (function, functionCode) = await CreateComplexTestFunctionAsync();

            // Act
            var executor = new FlowExecutor(functionCode.Code);
            var context = CreateTestContext();
            context.globalData["counter"] = 5; // 设置条件变量

            var result = await executor.Run(context);

            // Assert
            result.Should().NotBeNull("复杂工作流应该返回结果");
            context.Current.Result.Should().NotBeNull("工作流上下文应该有结果");
            Output.WriteLine($"复杂工作流执行结果: {JsonHelper.Serialize(result)}");
        }

        [Fact]
        public async Task WorkflowWithPersistence_ShouldCreateRunInstance()
        {
            // Arrange
            await InitializeTestDataAsync();
            var (function, functionCode) = await CreateComplexTestFunctionAsync(); // 使用持久化工作流

            // Act
            var executor = new FlowExecutor(functionCode.Code);
            var context = CreateTestContext();
            context.Persistence = true;

            var result = await executor.Run(context);

            // Assert
            result.Should().NotBeNull("持久化工作流应该返回结果");

            // 验证是否创建了运行实例记录
            using var flowDb = GetService<GcpFlowDb>();
            var runProcs = await flowDb.LcFruProcs
                .Where(p => p.SolutionId == "test-solution-001" && p.ProjectId == "test-project-001")
                .ToListAsync();

            runProcs.Should().NotBeEmpty("应该创建工作流运行实例记录");
            Output.WriteLine($"创建的运行实例数量: {runProcs.Count}");
        }

        [Fact]
        public async Task WorkflowExecution_ShouldHandleErrors()
        {
            // Arrange
            await InitializeTestDataAsync();
            var invalidWorkflowJson = CreateInvalidWorkflowJson();
            
            // Act & Assert
            var action = () => new FlowExecutor(invalidWorkflowJson);
            action.Should().NotThrow("创建执行器不应该抛出异常，错误应该在运行时处理");
        }

        [Fact]
        public async Task WorkflowWithMiddleware_ShouldApplyMiddleware()
        {
            // Arrange
            await InitializeTestDataAsync();
            var workflowWithMiddleware = CreateWorkflowWithMiddleware();
            
            // Act
            var executor = new FlowExecutor(workflowWithMiddleware);
            var context = CreateTestContext();
            
            var result = await executor.Run(context);

            // Assert
            result.Should().NotBeNull("带中间件的工作流应该返回结果");
            context.Middlewares.Should().NotBeEmpty("应该应用中间件");
        }

        [Fact]
        public async Task WorkflowStep_WithResult_ShouldBindOutputCorrectly()
        {
            // Arrange
            await InitializeTestDataAsync();
            var workflowWithOutput = CreateWorkflowWithOutput();
            
            // Act
            var executor = new FlowExecutor(workflowWithOutput);
            var context = CreateTestContext();
            
            var result = await executor.Run(context);

            // Assert
            result.Should().NotBeNull("带输出的工作流应该返回结果");
            context.globalData.Should().ContainKey("step-001", "应该包含步骤输出数据");
        }

        [Fact]
        public async Task WorkflowExecution_WithRootNodeBinding_ShouldHandleCorrectly()
        {
            // Arrange
            await InitializeTestDataAsync();
            var workflowWithRootNode = CreateWorkflowWithRootNodeOutput();

            // Act
            var executor = new FlowExecutor(workflowWithRootNode);
            var context = CreateTestContext();

            var result = await executor.Run(context);

            // Assert
            result.Should().NotBeNull("带ROOT节点绑定的工作流应该返回结果");
            // 根据ROOT节点处理逻辑，应该直接返回原始结果而不包装在ROOT对象中
            Output.WriteLine($"ROOT节点绑定结果: {JsonHelper.Serialize(result)}");
        }

        [Fact]
        public void FlowUtils_BindResult_WithRootNodeChildren_ShouldFlattenOutput()
        {
            // Arrange
            var mockResult = new Dictionary<string, object>
            {
                ["code"] = 200,
                ["data"] = new Dictionary<string, object>
                {
                    ["scanMessage"] = "未找到工序编码(null)对应的工序信息，请检查",
                    ["scanSuccess"] = false
                },
                ["message"] = (string?)null
            };

            var flowDatas = new List<FlowData>
            {
                new FlowData
                {
                    Key = "ROOT",
                    Type = "object",
                    Value = new DataValue { Type = "variable", VariableType = "current", VariableValue = "result" },
                    Children = new List<FlowData>
                    {
                        new FlowData
                        {
                            Key = "code",
                            Type = "number",
                            Value = new DataValue { Type = "variable", VariableType = "current", VariableValue = "result.code" }
                        },
                        new FlowData
                        {
                            Key = "data",
                            Type = "object",
                            Children = new List<FlowData>
                            {
                                new FlowData
                                {
                                    Key = "scanMessage",
                                    Type = "string",
                                    Value = new DataValue { Type = "variable", VariableType = "current", VariableValue = "result.data.scanMessage" }
                                },
                                new FlowData
                                {
                                    Key = "scanSuccess",
                                    Type = "boolean",
                                    Value = new DataValue { Type = "variable", VariableType = "current", VariableValue = "result.data.scanSuccess" }
                                }
                            }
                        },
                        new FlowData
                        {
                            Key = "message",
                            Type = "string",
                            Value = new DataValue { Type = "variable", VariableType = "current", VariableValue = "result.message" }
                        },
                        new FlowData
                        {
                            Key = "D4000",
                            Type = "number",
                            Value = new DataValue { Type = "text", TextValue = "3" }
                        }
                    }
                }
            };

            var stepResultDic = new Dictionary<string, object>();
            var engine = new Jint.Engine();
            var context = CreateTestContext();

            // Act
            FlowUtils.BindResult(flowDatas, stepResultDic, engine, context, mockResult);

            // Assert
            stepResultDic.Should().NotBeNull("绑定结果不应该为空");
            Output.WriteLine($"stepResultDic内容: {JsonHelper.Serialize(stepResultDic)}");
            stepResultDic.Should().ContainKey("ROOT", "应该包含ROOT键");

            var rootValue = stepResultDic["ROOT"] as IDictionary<string, object>;
            Output.WriteLine($"rootValue内容: {JsonHelper.Serialize(rootValue)}");
            rootValue.Should().NotBeNull("ROOT值应该是字典类型");
            rootValue.Should().ContainKey("code", "应该包含code字段");
            rootValue.Should().ContainKey("data", "应该包含data字段");
            rootValue.Should().ContainKey("message", "应该包含message字段");
            // D4000字段是固定值，应该被正确绑定
            // 注意：在实际测试中，D4000可能需要更复杂的绑定逻辑
            // 这里我们主要验证ROOT节点的展开逻辑是否正确

            // 验证data字段应该包含scanMessage和scanSuccess
            var dataDict = rootValue["data"] as IDictionary<string, object>;
            dataDict.Should().NotBeNull("data应该是字典类型");
            dataDict.Should().ContainKey("scanMessage", "data应该包含scanMessage字段");
            dataDict.Should().ContainKey("scanSuccess", "data应该包含scanSuccess字段");

            Output.WriteLine($"ROOT节点子字段绑定结果: {JsonHelper.Serialize(stepResultDic)}");
        }

        /// <summary>
        /// 创建测试上下文
        /// </summary>
        private FunctionContext CreateTestContext()
        {
            var context = new FunctionContext
            {
                globalData = new Dictionary<string, object>(),
                Args = new Dictionary<string, object>(),
                Persistence = false,
                trackId = TUID.NewTUID().ToString()
            };

            var dbContext = GetService<IDbContext>();
            context.LocalDbContext.Value = dbContext;

            // 初始化Current属性
            context.Current = new FunctionProvider
            {
                FunctionName = "测试工作流",
                Level = 0,
                SeqNo = 0,
                IsFlow = true
            };

            return context;
        }

        /// <summary>
        /// 创建测试函数
        /// </summary>
        private async Task<(LcFunction function, LcFunctionCode functionCode)> CreateTestFunctionAsync()
        {
            var function = TestDataBuilder.CreateTestFunction("简单测试工作流");
            var functionCode = TestDataBuilder.CreateTestFunctionCode(function.Id, TestDataBuilder.CreateSimpleWorkflowJson());

            using var db = GetService<GcpDb>();
            await db.InsertAsync(function);
            await db.InsertAsync(functionCode);

            return (function, functionCode);
        }

        /// <summary>
        /// 创建复杂测试函数
        /// </summary>
        private async Task<(LcFunction function, LcFunctionCode functionCode)> CreateComplexTestFunctionAsync()
        {
            var function = TestDataBuilder.CreateTestFunction("复杂测试工作流");
            var functionCode = TestDataBuilder.CreateTestFunctionCode(function.Id, TestDataBuilder.CreateComplexWorkflowJson());

            using var db = GetService<GcpDb>();
            await db.InsertAsync(function);
            await db.InsertAsync(functionCode);

            return (function, functionCode);
        }

        /// <summary>
        /// 创建无效的工作流JSON
        /// </summary>
        private string CreateInvalidWorkflowJson()
        {
            return "{ \"invalid\": \"json\" }";
        }

        /// <summary>
        /// 创建带中间件的工作流
        /// </summary>
        private string CreateWorkflowWithMiddleware()
        {
            var workflow = new
            {
                id = "test-middleware-workflow",
                version = 1,
                middleware = new[] { "logging", "timing" },
                data = new object[0],
                body = new[]
                {
                    new
                    {
                        id = "step-001",
                        name = "测试步骤",
                        function = "controlDelay",
                        args = new { milliseconds = 100 }
                    }
                }
            };

            return JsonHelper.Serialize(workflow);
        }

        /// <summary>
        /// 创建带输出的工作流
        /// </summary>
        private string CreateWorkflowWithOutput()
        {
            var workflow = new
            {
                id = "test-output-workflow",
                version = 1,
                data = new object[0],
                body = new[]
                {
                    new
                    {
                        id = "step-001",
                        name = "输出步骤",
                        function = "controlDelay",
                        args = new { milliseconds = 100 },
                        result = new[]
                        {
                            new
                            {
                                key = "output1",
                                type = "string",
                                value = new { type = "text", textValue = "test output" }
                            }
                        }
                    }
                }
            };

            return JsonHelper.Serialize(workflow);
        }

        /// <summary>
        /// 创建带ROOT节点输出的工作流
        /// </summary>
        private string CreateWorkflowWithRootNodeOutput()
        {
            var workflow = new
            {
                id = "test-root-workflow",
                version = 1,
                data = new object[0],
                body = new[]
                {
                    new
                    {
                        id = "step-001",
                        name = "ROOT输出步骤",
                        function = "controlDelay",
                        args = new { milliseconds = 100 },
                        result = new[]
                        {
                            new
                            {
                                key = "ROOT",
                                type = "object",
                                value = new { type = "text", textValue = "root output" }
                            }
                        }
                    }
                }
            };

            return JsonHelper.Serialize(workflow);
        }

        /// <summary>
        /// 创建带ROOT节点子字段的工作流，模拟用户遇到的具体情况
        /// </summary>
        private static string CreateWorkflowWithRootNodeChildren()
        {
            var workflow = new
            {
                id = "test-root-children-workflow",
                version = 1,
                data = Array.Empty<object>(),
                body = new[]
                {
                    new
                    {
                        id = "step-001",
                        name = "API请求步骤",
                        function = "scriptJs",
                        args = new {
                            script = @"
                                return {
                                    code: 200,
                                    data: {
                                        scanMessage: '未找到工序编码(null)对应的工序信息，请检查',
                                        scanSuccess: false
                                    },
                                    message: null
                                };
                            "
                        },
                        result = new[]
                        {
                            new
                            {
                                key = "ROOT",
                                type = "object",
                                children = new object[]
                                {
                                    new
                                    {
                                        key = "code",
                                        type = "number",
                                        value = new { type = "variable", variableType = "current", variableValue = "result.code" }
                                    },
                                    new
                                    {
                                        key = "data",
                                        type = "object",
                                        children = new object[]
                                        {
                                            new
                                            {
                                                key = "scanMessage",
                                                type = "string",
                                                value = new { type = "variable", variableType = "current", variableValue = "result.data.scanMessage" }
                                            },
                                            new
                                            {
                                                key = "scanSuccess",
                                                type = "boolean",
                                                value = new { type = "variable", variableType = "current", variableValue = "result.data.scanSuccess" }
                                            }
                                        }
                                    },
                                    new
                                    {
                                        key = "message",
                                        type = "string",
                                        value = new { type = "variable", variableType = "current", variableValue = "result.message" }
                                    },
                                    new
                                    {
                                        key = "D4000",
                                        type = "number",
                                        value = new { type = "text", textValue = "3" }
                                    }
                                }
                            }
                        }
                    }
                }
            };

            return JsonHelper.Serialize(workflow);
        }

        protected override async Task SeedTestDataAsync(GcpDb db)
        {
            try
            {
                // 创建基础测试数据 - 使用唯一ID避免冲突
                var timestamp = DateTime.Now.Ticks.ToString().Substring(10); // 使用时间戳后8位
                var solutionId = $"test-sol-exec-{timestamp}";
                var projectId = $"test-proj-exec-{timestamp}";

                var solution = TestDataBuilder.CreateTestSolution("工作流执行测试解决方案");
                solution.Id = solutionId;
                await db.InsertAsync(solution);

                var project = TestDataBuilder.CreateTestProject("工作流执行测试项目", solutionId);
                project.Id = projectId;
                await db.InsertAsync(project);

                // 创建测试函数
                var functions = new[]
                {
                    TestDataBuilder.CreateTestFunction("简单工作流函数", solutionId, projectId),
                    TestDataBuilder.CreateTestFunction("复杂工作流函数", solutionId, projectId),
                    TestDataBuilder.CreateTestFunction("持久化工作流函数", solutionId, projectId),
                    TestDataBuilder.CreateTestFunction("错误处理工作流函数", solutionId, projectId)
                };

                functions[0].Id = $"test-func-simple-{timestamp}";
                functions[1].Id = $"test-func-complex-{timestamp}";
                functions[2].Id = $"test-func-persist-{timestamp}";
                functions[3].Id = $"test-func-error-{timestamp}";

                // 插入函数
                foreach (var function in functions)
                {
                    await db.InsertAsync(function);
                }

                // 创建函数代码
                var functionCodes = new[]
                {
                    TestDataBuilder.CreateTestFunctionCode(functions[0].Id, TestDataBuilder.CreateSimpleWorkflowJson()),
                    TestDataBuilder.CreateTestFunctionCode(functions[1].Id, TestDataBuilder.CreateComplexWorkflowJson()),
                    TestDataBuilder.CreateTestFunctionCode(functions[2].Id, TestDataBuilder.CreateSimpleWorkflowJson()),
                    TestDataBuilder.CreateTestFunctionCode(functions[3].Id, TestDataBuilder.CreateSimpleWorkflowJson()) // 使用简单工作流代替错误工作流
                };

                foreach (var functionCode in functionCodes)
                {
                    await db.InsertAsync(functionCode);
                }

                Output.WriteLine("工作流执行测试数据初始化完成");
            }
            catch (Exception ex)
            {
                Output.WriteLine($"工作流执行测试数据初始化失败: {ex.Message}");
                throw;
            }
        }
    }
}
