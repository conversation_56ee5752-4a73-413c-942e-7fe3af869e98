﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
		<AppendRuntimeIdentifierToOutputPath>false</AppendRuntimeIdentifierToOutputPath>
		<UserSecretsId>deed39a8-a92e-4386-8092-63c83478d18e</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerfileContext>..\..</DockerfileContext>
		<EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>
		<!-- 禁用静态 Web 资产系统 -->
		<StaticWebAssetsEnabled>false</StaticWebAssetsEnabled>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<OutputPath>..\..\dist\</OutputPath>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<OutputPath>..\..\dist\</OutputPath>
		<DebugType>none</DebugType>
		<DebugSymbols>false</DebugSymbols>
	</PropertyGroup>

	<ItemGroup>
		<AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
			<_Parameter1>GCP.Tests</_Parameter1>
		</AssemblyAttribute>
	</ItemGroup>

	<ItemGroup>
		<Compile Remove="logs\**" />
		<Content Remove="logs\**" />
		<EmbeddedResource Remove="logs\**" />
		<None Remove="logs.db" />
		<None Remove="logs\**" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\GCP.Core\GCP.Core.csproj" />
	  <TrimmerRootAssembly Include="MySqlConnector" />
	</ItemGroup>
</Project>
