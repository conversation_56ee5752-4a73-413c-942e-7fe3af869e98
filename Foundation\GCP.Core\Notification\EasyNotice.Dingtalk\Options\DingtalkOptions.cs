﻿namespace EasyNotice
{
    public class DingtalkOptions
    {
        public const string SectionName = "DingTalk";

        /// <summary>
        /// 通知类型：robot-群机器人，workNotice-工作通知
        /// </summary>
        public string NotificationType { get; set; } = "robot";

        // 群机器人配置
        public string WebHook { get; set; }

        public string Secret { get; set; }

        // 工作通知配置
        /// <summary>
        /// 企业内部应用的AppKey
        /// </summary>
        public string AppKey { get; set; }

        /// <summary>
        /// 企业内部应用的AppSecret
        /// </summary>
        public string AppSecret { get; set; }

        /// <summary>
        /// 应用的AgentId
        /// </summary>
        public long AgentId { get; set; }

        /// <summary>
        /// 接收通知的用户ID列表
        /// </summary>
        public List<string> UserIds { get; set; } = new();

        /// <summary>
        /// 接收通知的部门ID列表
        /// </summary>
        public List<string> DeptIds { get; set; } = new();

        /// <summary>
        /// 是否发送给全员
        /// </summary>
        public bool ToAllUser { get; set; }
    }
}
