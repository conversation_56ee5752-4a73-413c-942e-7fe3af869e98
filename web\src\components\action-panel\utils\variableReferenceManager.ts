import { FlowData, FlowDataValue, VariableReference } from '../model';
import { findVariableByPath } from './variableProcessor';

/**
 * 变量引用管理器
 * 负责管理变量引用关系，提供实时解析和同步更新功能
 */
export class VariableReferenceManager {
  private static instance: VariableReferenceManager;

  // 变量引用映射表：variableId -> Set<引用位置>
  private referenceMap = new Map<string, Set<string>>();

  // 变量信息缓存：variableId -> VariableReference
  private variableCache = new Map<string, VariableReference>();

  // 引用位置映射：引用位置 -> FlowDataValue
  private valueMap = new Map<string, FlowDataValue>();

  // 变更监听器
  private changeListeners = new Set<(variableId: string, reference: VariableReference) => void>();

  static getInstance(): VariableReferenceManager {
    if (!this.instance) {
      this.instance = new VariableReferenceManager();
    }
    return this.instance;
  }

  /**
   * 注册变量引用
   * @param valueId 引用位置的唯一标识
   * @param dataValue FlowDataValue对象
   * @param variableData 变量数据
   */
  registerReference(valueId: string, dataValue: FlowDataValue, variableData: FlowData): void {
    if (dataValue.type !== 'variable' || !variableData) return;

    const variableId = this.generateVariableId(variableData);
    const reference: VariableReference = {
      id: variableId,
      path: variableData.path || variableData.key,
      type: variableData.type,
      name: variableData.pathDescription || variableData.description || variableData.key,
      description: variableData.description,
      source: dataValue.variableType || 'current',
    };

    // 更新引用映射
    if (!this.referenceMap.has(variableId)) {
      this.referenceMap.set(variableId, new Set());
    }
    this.referenceMap.get(variableId)!.add(valueId);

    // 更新变量缓存
    this.variableCache.set(variableId, reference);

    // 更新引用位置映射
    this.valueMap.set(valueId, dataValue);

    // 设置引用ID和路径
    dataValue.variableRefId = variableId;
  }

  /**
   * 移除变量引用
   * @param valueId 引用位置的唯一标识
   */
  unregisterReference(valueId: string): void {
    const dataValue = this.valueMap.get(valueId);
    if (!dataValue || !dataValue.variableRefId) return;

    const variableId = dataValue.variableRefId;
    const references = this.referenceMap.get(variableId);

    if (references) {
      references.delete(valueId);
      if (references.size === 0) {
        this.referenceMap.delete(variableId);
        this.variableCache.delete(variableId);
      }
    }

    this.valueMap.delete(valueId);
  }

  /**
   * 获取变量引用信息
   * @param variableId 变量ID
   */
  getVariableReference(variableId: string): VariableReference | undefined {
    return this.variableCache.get(variableId);
  }

  /**
   * 获取变量引用信息（通过FlowDataValue）
   * @param dataValue FlowDataValue对象
   * @param availableVariables 可用的变量列表，用于自动初始化
   */
  getVariableReferenceByValue(
    dataValue: FlowDataValue,
    availableVariables?: FlowData[],
  ): VariableReference | undefined {
    if (dataValue.type !== 'variable') return undefined;

    // 如果已有引用ID，检查引用是否仍然有效
    if (dataValue.variableRefId) {
      const existingReference = this.getVariableReference(dataValue.variableRefId);
      if (existingReference) {
        return existingReference;
      } else {
        // 引用失效，清除引用ID，尝试重新解析
        console.log('变量引用失效，尝试重新解析:', dataValue.variableRefId);
        dataValue.variableRefId = undefined;
      }
    }

    // 如果没有引用ID但有variableValue，尝试自动初始化
    if (dataValue.variableValue && availableVariables) {
      const matchedVariable = findVariableByPath(dataValue.variableValue, availableVariables);
      if (matchedVariable) {
        // 更新 dataValue 的路径信息为处理后的数据
        const processedPath = matchedVariable.path || matchedVariable.key;
        const processedName = matchedVariable.pathDescription || matchedVariable.description || matchedVariable.key;

        // 更新变量值和名称为处理后的数据
        dataValue.variableValue = processedPath;
        dataValue.variableName = processedName;
        dataValue.dataType = matchedVariable.type;

        // 自动注册引用
        const valueId = this.generateValueId();
        this.registerReference(valueId, dataValue, matchedVariable);
        return this.getVariableReference(dataValue.variableRefId!);
      } else {
        // 清除失效的引用信息，但保留原始的变量名和值用于错误显示
        dataValue.variableRefId = undefined;
      }
    }

    return undefined;
  }

  /**
   * 更新变量信息
   * @param oldVariableData 旧的变量数据
   * @param newVariableData 新的变量数据
   */
  updateVariable(oldVariableData: FlowData, newVariableData: FlowData): void {
    const oldVariableId = this.generateVariableId(oldVariableData);
    const newVariableId = this.generateVariableId(newVariableData);

    const references = this.referenceMap.get(oldVariableId);
    if (!references) return;

    const newReference: VariableReference = {
      id: newVariableId,
      path: newVariableData.path || newVariableData.key,
      type: newVariableData.type,
      name: newVariableData.pathDescription || newVariableData.description || newVariableData.key,
      description: newVariableData.description,
      source: this.variableCache.get(oldVariableId)?.source || 'current',
    };

    // 如果变量ID发生变化，需要更新映射
    if (oldVariableId !== newVariableId) {
      this.referenceMap.set(newVariableId, references);
      this.referenceMap.delete(oldVariableId);
      this.variableCache.delete(oldVariableId);
    }

    // 更新变量缓存
    this.variableCache.set(newVariableId, newReference);

    // 更新所有引用该变量的FlowDataValue
    references.forEach((valueId) => {
      const dataValue = this.valueMap.get(valueId);
      if (dataValue) {
        dataValue.variableRefId = newVariableId;
        dataValue.dataType = newReference.type;
        dataValue.variableName = newReference.name;
        dataValue.variableValue = newReference.path;
      }
    });

    // 通知监听器
    this.notifyChange(newVariableId, newReference);
  }

  /**
   * 删除变量
   * @param variableData 变量数据
   */
  deleteVariable(variableData: FlowData): void {
    const variableId = this.generateVariableId(variableData);
    const references = this.referenceMap.get(variableId);

    if (references) {
      // 清空所有引用该变量的FlowDataValue
      references.forEach((valueId) => {
        const dataValue = this.valueMap.get(valueId);
        if (dataValue) {
          // 重置为文本类型
          dataValue.type = 'text';
          dataValue.textValue = '';
          dataValue.variableRefId = undefined;
          dataValue.variableType = '';
          dataValue.variableName = '';
          dataValue.variableValue = '';
          dataValue.dataType = '';
        }
      });

      this.referenceMap.delete(variableId);
      this.variableCache.delete(variableId);
    }
  }

  /**
   * 添加变更监听器
   * @param listener 监听器函数
   */
  addChangeListener(listener: (variableId: string, reference: VariableReference) => void): void {
    this.changeListeners.add(listener);
  }

  /**
   * 移除变更监听器
   * @param listener 监听器函数
   */
  removeChangeListener(listener: (variableId: string, reference: VariableReference) => void): void {
    this.changeListeners.delete(listener);
  }

  /**
   * 通知变更
   * @param variableId 变量ID
   * @param reference 变量引用信息
   */
  private notifyChange(variableId: string, reference: VariableReference): void {
    this.changeListeners.forEach((listener) => {
      try {
        listener(variableId, reference);
      } catch (error) {
        console.error('变量引用监听器执行错误:', error);
      }
    });
  }

  /**
   * 生成值ID
   */
  private generateValueId(): string {
    return `value_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 生成变量ID
   * @param variableData 变量数据
   */
  private generateVariableId(variableData: FlowData): string {
    // 获取路径，去掉最后的 .key 部分
    let path = variableData.path || variableData.key;
    if (path && path.includes('.')) {
      // 去掉最后的 .key 部分，例如 "Values.5530" -> "Values"
      const pathParts = path.split('.');
      if (pathParts.length > 1) {
        path = pathParts.slice(0, -1).join('.');
      }
    }

    // 格式：path_id，例如 "Values_SXxEsWdOmlAIA"
    const generatedId = `${path}_${variableData.id}`;

    return generatedId;
  }

  /**
   * 清空所有引用
   */
  clear(): void {
    this.referenceMap.clear();
    this.variableCache.clear();
    this.valueMap.clear();
    this.changeListeners.clear();
  }

  /**
   * 序列化变量引用信息用于持久化
   * 只序列化 local 类型的变量引用，current 和 global 类型不持久化
   * @returns 序列化后的变量引用映射表
   */
  serialize(): Record<string, VariableReference> {
    const result: Record<string, VariableReference> = {};
    let localCount = 0;

    this.variableCache.forEach((reference, variableId) => {
      // 只持久化 local 类型的变量引用
      if (reference.source === 'local') {
        result[variableId] = { ...reference };
        localCount++;
      }
    });

    console.log(`序列化完成: ${localCount} 个 local 引用被持久化`);
    return result;
  }

  /**
   * 从序列化数据恢复变量引用信息
   * @param serializedReferences 序列化的变量引用映射表
   */
  deserialize(serializedReferences: Record<string, VariableReference>): void {
    if (!serializedReferences) return;

    Object.entries(serializedReferences).forEach(([variableId, reference]) => {
      this.variableCache.set(variableId, reference);
    });
  }

  /**
   * 批量初始化变量引用
   * 扫描所有FlowDataValue，为已有variableRefId的变量重建引用关系
   * 只恢复 local 类型的持久化引用，current 和 global 类型实时重建
   * @param flowInfo 流程信息
   */
  batchInitializeReferences(flowInfo: any): void {
    if (!flowInfo) return;

    console.log('开始批量初始化变量引用...');

    // 先清空现有引用
    this.clear();

    // 恢复序列化的 local 类型引用信息
    if (flowInfo.variableReferences) {
      this.deserialize(flowInfo.variableReferences);
      console.log(`从持久化数据恢复了 ${Object.keys(flowInfo.variableReferences).length} 个 local 变量引用定义`);
    }

    // 扫描所有FlowStep中的FlowDataValue
    const allDataValues: Array<{ dataValue: any; stepId: string; path: string }> = [];

    // 收集所有FlowDataValue
    this.collectDataValuesFromFlowInfo(flowInfo, allDataValues);
    console.log(`扫描到 ${allDataValues.length} 个数据值`);

    let restoredCount = 0;
    let rebuiltCount = 0;
    let failedCount = 0;

    // 为每个有variableRefId的FlowDataValue重建引用关系
    allDataValues.forEach(({ dataValue, stepId, path }) => {
      if (dataValue?.type === 'variable' && dataValue.variableRefId) {
        const valueId = this.generateValueId();

        // 检查引用是否存在于缓存中（local 类型的持久化引用）
        const reference = this.variableCache.get(dataValue.variableRefId);
        if (reference) {
          // 重建引用映射关系
          if (!this.referenceMap.has(dataValue.variableRefId)) {
            this.referenceMap.set(dataValue.variableRefId, new Set());
          }
          this.referenceMap.get(dataValue.variableRefId)!.add(valueId);
          this.valueMap.set(valueId, dataValue);

          restoredCount++;
          console.log(
            `✓ 恢复 local 变量引用: ${reference.path} (${dataValue.variableRefId}) 在步骤 ${stepId} 的 ${path}`,
          );
        } else {
          // 对于 current 和 global 类型，尝试实时重建引用
          const rebuilt = this.tryRebuildNonLocalReference(dataValue, flowInfo);
          if (rebuilt) {
            rebuiltCount++;
            console.log(
              `✓ 重建 ${dataValue.variableType} 变量引用: ${dataValue.variableValue} 在步骤 ${stepId} 的 ${path}`,
            );
          } else {
            failedCount++;
            console.warn(
              `✗ 变量引用失效: ${dataValue.variableRefId} (${dataValue.variableType}) 在步骤 ${stepId} 的 ${path}`,
            );
          }
        }
      }
    });

    console.log(
      `批量初始化完成: 恢复 ${restoredCount} 个 local 引用，重建 ${rebuiltCount} 个 current/global 引用，失败 ${failedCount} 个`,
    );
  }

  /**
   * 尝试重建非 local 类型的变量引用
   * 对于 current 和 global 类型的变量，实时重建引用关系
   * @param dataValue FlowDataValue对象
   * @param flowInfo 流程信息
   * @returns 是否成功重建
   */
  private tryRebuildNonLocalReference(dataValue: any, flowInfo: any): boolean {
    if (!dataValue.variableValue || !dataValue.variableType) return false;

    // current 类型：从当前步骤的输出变量中查找
    if (dataValue.variableType === 'current') {
      return this.rebuildCurrentReference(dataValue, flowInfo);
    }

    // global 类型：从全局变量中查找
    if (dataValue.variableType === 'global') {
      return this.rebuildGlobalReference(dataValue, flowInfo);
    }

    return false;
  }

  /**
   * 重建 current 类型的变量引用
   * @param dataValue FlowDataValue对象
   * @param flowInfo 流程信息
   * @returns 是否成功重建
   */
  private rebuildCurrentReference(dataValue: any, flowInfo: any): boolean {
    // current 类型的变量需要根据当前上下文动态解析
    // 这里可以根据具体的业务逻辑来实现
    // 暂时清除引用ID，让系统在运行时重新解析
    dataValue.variableRefId = undefined;
    return true;
  }

  /**
   * 重建 global 类型的变量引用
   * @param dataValue FlowDataValue对象
   * @param flowInfo 流程信息
   * @returns 是否成功重建
   */
  private rebuildGlobalReference(dataValue: any, flowInfo: any): boolean {
    // global 类型的变量需要实时从全局变量中获取
    // 清除引用ID，让系统在运行时重新解析
    dataValue.variableRefId = undefined;
    return true;
  }

  /**
   * 尝试自动恢复变量引用
   * 当变量引用失效时，尝试通过变量值重新建立引用
   * @param dataValue FlowDataValue对象
   * @param flowInfo 流程信息
   */
  private tryAutoRestoreReference(dataValue: any, flowInfo: any): void {
    if (!dataValue.variableValue || !flowInfo.data) return;

    // 在全局变量中查找匹配的变量
    const matchedVariable = flowInfo.data.find(
      (variable: any) => variable.path === dataValue.variableValue || variable.key === dataValue.variableValue,
    );

    if (matchedVariable) {
      // 重新注册变量引用
      const valueId = this.generateValueId();
      this.registerReference(valueId, dataValue, matchedVariable);
      console.log(`✓ 自动恢复变量引用: ${matchedVariable.path || matchedVariable.key}`);
    }
  }

  /**
   * 从FlowInfo中收集所有FlowDataValue
   * @param flowInfo 流程信息
   * @param collector 收集器数组
   */
  private collectDataValuesFromFlowInfo(
    flowInfo: any,
    collector: Array<{ dataValue: any; stepId: string; path: string }>,
  ): void {
    if (!flowInfo.body) return;

    flowInfo.body.forEach((step: any) => {
      this.collectDataValuesFromObject(step, collector, step.id, 'step');
    });
  }

  /**
   * 递归收集对象中的所有FlowDataValue
   * @param obj 要扫描的对象
   * @param collector 收集器数组
   * @param stepId 步骤ID
   * @param currentPath 当前路径
   */
  private collectDataValuesFromObject(
    obj: any,
    collector: Array<{ dataValue: any; stepId: string; path: string }>,
    stepId: string,
    currentPath: string,
  ): void {
    if (!obj || typeof obj !== 'object') return;

    // 检查是否是FlowDataValue
    if (obj.type && ['text', 'variable', 'script', 'visual'].includes(obj.type)) {
      collector.push({
        dataValue: obj,
        stepId,
        path: currentPath,
      });
      return;
    }

    // 递归扫描对象属性
    Object.entries(obj).forEach(([key, value]) => {
      if (value && typeof value === 'object') {
        const newPath = currentPath ? `${currentPath}.${key}` : key;

        if (Array.isArray(value)) {
          value.forEach((item, index) => {
            this.collectDataValuesFromObject(item, collector, stepId, `${newPath}[${index}]`);
          });
        } else {
          this.collectDataValuesFromObject(value, collector, stepId, newPath);
        }
      }
    });
  }

  /**
   * 获取变量引用统计信息
   */
  getStatistics(): {
    totalReferences: number;
    totalVariables: number;
    totalValueMappings: number;
    activeListeners: number;
  } {
    return {
      totalReferences: Array.from(this.referenceMap.values()).reduce((sum, set) => sum + set.size, 0),
      totalVariables: this.variableCache.size,
      totalValueMappings: this.valueMap.size,
      activeListeners: this.changeListeners.size,
    };
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    const stats = this.getStatistics();
    return {
      statistics: stats,
      references: Array.from(this.referenceMap.entries()).map(([variableId, valueIds]) => ({
        variableId,
        valueIds: Array.from(valueIds),
        variable: this.variableCache.get(variableId),
      })),
      orphanedValues: Array.from(this.valueMap.entries()).filter(
        ([valueId]) => !Array.from(this.referenceMap.values()).some((set) => set.has(valueId)),
      ),
    };
  }

  /**
   * 验证引用完整性
   * 检查是否存在孤立的引用或不一致的状态
   */
  validateIntegrity(): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // 检查引用映射和值映射的一致性
    this.referenceMap.forEach((valueIds, variableId) => {
      valueIds.forEach((valueId) => {
        if (!this.valueMap.has(valueId)) {
          issues.push(`引用映射中的值ID ${valueId} 在值映射中不存在`);
        }
      });

      if (!this.variableCache.has(variableId)) {
        issues.push(`引用映射中的变量ID ${variableId} 在变量缓存中不存在`);
      }
    });

    // 检查值映射中的孤立值
    this.valueMap.forEach((dataValue, valueId) => {
      const found = Array.from(this.referenceMap.values()).some((set) => set.has(valueId));
      if (!found) {
        issues.push(`值映射中的值ID ${valueId} 没有对应的引用映射`);
      }
    });

    return {
      isValid: issues.length === 0,
      issues,
    };
  }
}

// 导出单例实例
export const variableReferenceManager = VariableReferenceManager.getInstance();

// 全局调试方法，方便在控制台中使用
if (typeof window !== 'undefined') {
  (window as any).debugVariableReferences = () => {
    const debugInfo = variableReferenceManager.getDebugInfo();
    const integrity = variableReferenceManager.validateIntegrity();

    console.group('🔗 变量引用调试信息');
    console.log('📊 统计信息:', debugInfo.statistics);
    console.log('🔍 引用详情:', debugInfo.references);

    if (debugInfo.orphanedValues.length > 0) {
      console.warn('⚠️ 孤立值:', debugInfo.orphanedValues);
    }

    if (!integrity.isValid) {
      console.error('❌ 完整性检查失败:', integrity.issues);
    } else {
      console.log('✅ 完整性检查通过');
    }

    console.groupEnd();

    return {
      debugInfo,
      integrity,
    };
  };
}
