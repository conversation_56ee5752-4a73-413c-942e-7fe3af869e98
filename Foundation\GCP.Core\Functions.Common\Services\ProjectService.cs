﻿using GCP.Common;
using GCP.DataAccess;
using LinqToDB;

namespace GCP.Functions.Common.Services
{
    [Function("project", "项目服务")]
    internal class ProjectService : BaseService
    {
        [Function("getAll", "获取所有项目清单根据解决方案ID")]
        public List<LcProject> GetAll(string[] solutionIds)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcProjects
                        where a.State == 1 && (solutionIds != null && solutionIds.Contains(a.SolutionId))
                        select a).ToList();
            return data;
        }

        [Function("getById", "根据项目ID获取项目信息")]
        public LcProject GetById(string id)
        {
            using var db = this.GetDb();
            var data = db.LcProjects.FirstOrDefault(a => a.Id == id);
            return data;
        }

        [Function("add", "添加项目")]
        public void Add(LcProject project)
        {
            using var db = this.GetDb();
            var data = db.LcProjects.FirstOrDefault(a =>
                a.ProjectName == project.ProjectName &&
                a.SolutionId == this.SolutionId);

            if (data != null)
            {
                if (data.State == 0)
                {
                    db.LcProjects.Delete(a => a.Id == data.Id);
                }
                else
                {
                    throw new CustomException("项目名称已存在，请勿重复");
                }
            }

            project.SolutionId = this.SolutionId;
            this.InsertData(project);
        }

        [Function("update", "更新项目")]
        public void Update(LcProject project)
        {
            this.UpdateData(project);
        }

        [Function("delete", "删除项目")]
        public void Delete(string id)
        {
            using var db = this.GetDb();
            var project = db.LcProjects.FirstOrDefault(a => a.Id == id);
            if (project == null)
            {
                return;
            }
            project.State = 0;
            this.UpdateData(project, db);
        }
    }
}
